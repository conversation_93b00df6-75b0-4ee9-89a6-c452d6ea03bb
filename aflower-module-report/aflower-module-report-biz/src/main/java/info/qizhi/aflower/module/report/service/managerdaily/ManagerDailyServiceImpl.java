package info.qizhi.aflower.module.report.service.managerdaily;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.number.NumberUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.tenant.core.aop.TenantIgnore;
import info.qizhi.aflower.module.pms.api.account.AccountApi;
import info.qizhi.aflower.module.pms.api.account.dto.AccountRespDTO;
import info.qizhi.aflower.module.pms.api.account.dto.HandoverReportReqDTO;
import info.qizhi.aflower.module.pms.api.account.dto.HandoverReportRespDTO;
import info.qizhi.aflower.module.pms.api.account.dto.PayOrConsumeDetailReqDTO;
import info.qizhi.aflower.module.report.controller.admin.bizdataclassstatdaily.vo.BizDataClassStatDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailReportReqVO;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailRespVO;
import info.qizhi.aflower.module.report.controller.admin.incomestatdaily.vo.IncomeStatDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.*;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyReportReqVO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyRespVO;
import info.qizhi.aflower.module.report.dal.dataobject.bizdataclassstatdaily.BizDataClassStatDailyDO;
import info.qizhi.aflower.module.report.dal.dataobject.incomestatdaily.IncomeStatDailyDO;
import info.qizhi.aflower.module.report.dal.dataobject.managerdaily.ManagerDailyDO;
import info.qizhi.aflower.module.report.dal.mysql.managerdaily.ManagerDailyMapper;
import info.qizhi.aflower.module.report.service.bizdataclassstatdaily.BizDataClassStatDailyService;
import info.qizhi.aflower.module.report.service.guestdetail.GuestDetailService;
import info.qizhi.aflower.module.report.service.incomestatdaily.IncomeStatDailyService;
import info.qizhi.aflower.module.report.service.incomestatmonth.IncomeStatMonthService;
import info.qizhi.aflower.module.report.service.roomratedaily.RoomRateDailyService;
import info.qizhi.aflower.module.system.api.dict.DictDataApi;
import info.qizhi.aflower.module.system.api.dict.dto.DictDataRespDTO;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.DATE_ERROR;

/**
 * 经理综合日报表(固化) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ManagerDailyServiceImpl implements ManagerDailyService {

    public static final String ORDERSRC = "订单来源";
    public static final String RTCODE = "房型";
    public static final String GSRC = "客源";
    public static final String CHANNEL = "渠道";
    public static final String INTYPE = "入住类型";

    @Resource
    private ManagerDailyMapper managerDailyMapper;

    @Resource
    private MerchantApi merchantApi;

    @Resource
    private AccountApi accountApi;

    @Resource
    private GuestDetailService guestDetailService;

    @Resource
    private RoomRateDailyService roomRateDailyService;
    @Resource
    private DictDataApi dictDataApi;
    @Resource
    private IncomeStatDailyService incomeStatDailyService;
    @Resource
    private IncomeStatMonthService incomeStatMonthService;
    @Resource
    private ManagerMonthService managerMonthService;
    @Resource
    private BizDataClassStatDailyService bizDataClassStatDailyService;

    // 总营业指标
    private static final String TOTAL_BUSINESS = "total_business";

    @Override
    public Long createManagerDaily(ManagerDailySaveReqVO createReqVO) {
        // 插入
        ManagerDailyDO managerDaily = BeanUtils.toBean(createReqVO, ManagerDailyDO.class);
        managerDailyMapper.insert(managerDaily);
        // 返回
        return managerDaily.getId();
    }

    @Override
    @TenantIgnore
    public ManagerDailyReportRespVO getManagerDailyReport(ManagerDailyReqVO reqVO) {
        ManagerDailyReportRespVO managerDailyReportRespVO = new ManagerDailyReportRespVO();
        // 门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        managerDailyReportRespVO.setHname(merchant.getHname())
                .setHcode(merchant.getHcode())
                .setBizDate(reqVO.getBizDate())
                .setOperator(reqVO.getOperator())
                .setLastSelectTime(LocalDateTime.now());
        // 获取客房营业收入指标信息
        ManagerDailyDO managerDaily = managerDailyMapper.selectOne(reqVO);
        ManagerDailyRespVO bean = BeanUtils.toBean(managerDaily, ManagerDailyRespVO.class);
        managerDailyReportRespVO.setRoomRevenueIndex(bean);
        // 构建收入统计信息
        RevenueCount revenueCount = getRevenueCount(reqVO);
        managerDailyReportRespVO.setRevenueCount(revenueCount);
        // 构建经营数据分类统计信息
        List<BusinessData> business = getBusinessData(reqVO);
        managerDailyReportRespVO.setBusinessData(business);
        return managerDailyReportRespVO;
    }

    @Override
    public ManagerDailyDO getManagerDaily(ManagerDailyReqVO reqVO) {
        return managerDailyMapper.selectOne(reqVO);
    }

    @Override
    public List<ManagerDailyDO> getManagerDailyList(ManagerDailyReqVO reqVO) {
        return managerDailyMapper.selectList(reqVO);
    }

    @Override
    public ManagerDailyReportMonthRespVO getManagerMonthDetailList(ManagerDailyReqVO reqVO) {
        ManagerDailyReportMonthRespVO managerDailyReportMonthRespVO = new ManagerDailyReportMonthRespVO();
        ManagerDailyMonthRespVO managerDailyMonthRespVO = new ManagerDailyMonthRespVO();
        // 门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        managerDailyReportMonthRespVO.setHname(merchant.getHname())
                .setHcode(merchant.getHcode())
                .setStartDate(reqVO.getStartDate())
                .setEndDate(reqVO.getEndDate())
                .setOperator(reqVO.getOperator())
                .setLastSelectTime(LocalDateTime.now());
        // 获取客房营业收入指标信息
        List<ManagerDailyDO> managerDailys = managerDailyMapper.selectList(reqVO);
        List<ManagerDailyRespVO> bean = BeanUtils.toBean(managerDailys, ManagerDailyRespVO.class);
        List<ManagerMonthDetailRespVO> roomRevenueList = BeanUtils.toBean(bean, ManagerMonthDetailRespVO.class);
        //managerDailyReportRespVO.setRoomRevenueIndex(bean);
        // 构建收入统计信息
        configureAccountCount(reqVO, roomRevenueList);

        // 构建经营数据分类统计信息
        Map<LocalDate, ManagerMonthDetailRespVO> managerMonthRespVOMap = CollectionUtils.convertMap(roomRevenueList, ManagerMonthDetailRespVO::getBizDate);
        configureClassificationData(reqVO, managerDailyMonthRespVO, managerMonthRespVOMap);

        managerDailyMonthRespVO.setRoomRevenueIndex(roomRevenueList);

        managerDailyReportMonthRespVO.setManagerDailyMonthRespVO(managerDailyMonthRespVO);
        return managerDailyReportMonthRespVO;
    }

    @Override
    public ManagerReportRespVO getManagerReport(ManagerDailyReqVO reqVO) {
        ManagerReportRespVO managerReportRespVO = new ManagerReportRespVO();
        // 门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        managerReportRespVO.setHname(merchant.getHname())
                .setHcode(merchant.getHcode())
                .setStartDate(reqVO.getBizDate())
                .setEndDate(reqVO.getBizDate())
                .setLastSelectTime(LocalDateTime.now());

        ManagerRespVO managerRespVO = new ManagerRespVO();
        // 获取客房营业收入指标信息
        //ManagerDailyDO managerDailyDO = managerDailyMapper.selectOne(reqVO);

        List<ManagerDailyDO> managerDailyDOS = managerDailyMapper.selectYearList(reqVO);

        /*List<ManagerMonthDO> managerMonthDataList = managerMonthService.getManagerMonthDataList(new ManagerMonthReqVO()
                .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setStartDate(reqVO.getBizDate().withDayOfYear(1)).setEndDate(reqVO.getBizDate().withDayOfMonth(1)));*/

        // 查询本年1月1号到bizDate,及去年1月1号到bizDate的收入统计
        /*List<IncomeStatMonthDO> incomeStatMonthDOList = incomeStatMonthService.getIncomeStatList(new IncomeStatReqVO()
                .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setStartDate(reqVO.getBizDate().withDayOfYear(1)).setEndDate(reqVO.getBizDate().withDayOfMonth(1)));*/
       /* // 计算本日累计发生并赋值
        List<IncomeStatDailyDO> incomeStatDailyList = incomeStatDailyService.getIncomeStatDailyList(new IncomeStatDailyReqVO()
                .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setStartDate(reqVO.getBizDate()).setEndDate(reqVO.getBizDate()));*/
        // 获取所有房费科目
        List<DictDataRespDTO> subCodeDictData = dictDataApi.getDictDataListByParentCode(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT_ROOM_FEE.getCode()).getData();
        // 添加预授权
        subCodeDictData.add(new DictDataRespDTO().setCode(PayAccountEnum.BANK_PRE_AUTH.getCode()).setLabel(PayAccountEnum.BANK_PRE_AUTH.getLabel()));
        subCodeDictData.add(new DictDataRespDTO().setCode(PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode()).setLabel(PayAccountEnum.SCAN_GUN_PRE_AUTH.getLabel()));
        // 查询本年1月1号到bizDate,及去年1月1号到去年的那个bizDate的收入统计
        List<IncomeStatDailyDO> incomeStatDailyList = incomeStatDailyService.getIncomeStatDailyListByYear(new IncomeStatDailyReqVO()
                .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setStartDate(reqVO.getBizDate()).setEndDate(reqVO.getBizDate()));
        //过滤出不含房费和预授权的incomeStatDailyList
        incomeStatDailyList = CollectionUtils.filterList(incomeStatDailyList, incomeStatDailyDO -> subCodeDictData.stream().noneMatch(dictData -> dictData.getCode().equals(incomeStatDailyDO.getSubCode())));

        // 过滤本日的数据
        List<ManagerDailyDO> currentDayManagerDailyDOS = CollectionUtils.filterList(managerDailyDOS, managerDailyDO -> managerDailyDO.getBizDate().equals(reqVO.getBizDate()));
        List<IncomeStatDailyDO> currentDayIncomeStatDailyDOS = CollectionUtils.filterList(incomeStatDailyList, incomeStatDailyDO -> incomeStatDailyDO.getBizDate().equals(reqVO.getBizDate()));
        managerRespVO.setIncomeStat(calculateIncomeStat(currentDayIncomeStatDailyDOS, currentDayManagerDailyDOS));
        
        // 过滤昨日的数据
        LocalDate yesterday = reqVO.getBizDate().minusDays(1);
        List<ManagerDailyDO> yesterdayManagerDailyDOS = CollectionUtils.filterList(managerDailyDOS, managerDailyDO -> managerDailyDO.getBizDate().equals(yesterday));
        List<IncomeStatDailyDO> yesterdayIncomeStatDailyDOS = CollectionUtils.filterList(incomeStatDailyList, incomeStatDailyDO -> incomeStatDailyDO.getBizDate().equals(yesterday));
        managerRespVO.setYesterdayIncomeStat(calculateIncomeStat(yesterdayIncomeStatDailyDOS, yesterdayManagerDailyDOS));
        
       /* // 其他费用用减去房费
        managerRespVO.getIncomeStat().setOtherFee(managerRespVO.getIncomeStat().getOtherFee() - managerRespVO.getIncomeStat().getRoomFee());*/
        // 计算本月，上月，本年，去年累计发生并赋值
        calculateMonthYearIncomeStat(incomeStatDailyList, managerDailyDOS, managerRespVO, reqVO.getBizDate());
        // 计算分类数据
        calculateClassificationData(managerRespVO, reqVO);
        return managerReportRespVO.setManagerRespVO(managerRespVO);
    }

    private void calculateClassificationData(ManagerRespVO managerRespVO, ManagerDailyReqVO reqVO) {
        List<BizDataClassStatDailyDO> yearlyBizDataClassStatDailyList = bizDataClassStatDailyService.getYearlyBizDataClassStatDailyList(new BizDataClassStatDailyReqVO()
                .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setStartDate(reqVO.getBizDate().withDayOfYear(1)).setEndDate(reqVO.getBizDate()));

        if (CollUtil.isEmpty(yearlyBizDataClassStatDailyList)) {
            return;
        }

        // 按统计类型分组数据
        Map<String, List<BizDataClassStatDailyDO>> groupedByStatType = yearlyBizDataClassStatDailyList.stream()
                .collect(Collectors.groupingBy(BizDataClassStatDailyDO::getStatType));

        // 如果指定了统计类型列表，则只保留指定的统计类型
        if (CollUtil.isNotEmpty(reqVO.getStatTypeList())) {
            groupedByStatType = groupedByStatType.entrySet().stream()
                    .filter(entry -> reqVO.getStatTypeList().contains(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        List<ClassStat> classStatList = new ArrayList<>();

        // 处理每个统计类型
        for (Map.Entry<String, List<BizDataClassStatDailyDO>> entry : groupedByStatType.entrySet()) {
            String statType = entry.getKey();
            List<BizDataClassStatDailyDO> statTypeData = entry.getValue();

            ClassStat classStat = new ClassStat();
            classStat.setStatType(statType);

            // 设置统计类型名称（从第一条数据中获取）
            if (!statTypeData.isEmpty()) {
                BizDataClassStatDailyDO lastData = statTypeData.getLast();
                classStat.setStatTypeName(lastData.getStatTypeName());
            }

            // 计算各种类型的数据
            List<ClassNightAndOccStatData> nightNumData = calculateNightNumData(statTypeData, reqVO.getBizDate());
            List<ClassFeeStatData> roomFeeData = calculateRoomFeeData(statTypeData, reqVO.getBizDate());

            classStat.setNightNumData(nightNumData);
            classStat.setOccData(calculateOccData(nightNumData, managerRespVO));
            classStat.setRoomFeeData(roomFeeData);
            classStat.setAvgRoomFeeData(calculateAvgRoomFeeData(nightNumData, roomFeeData));

            classStatList.add(classStat);
        }

        managerRespVO.setClassStatDataList(classStatList);
    }

    /**
     * 计算间夜数数据
     */
    private List<ClassNightAndOccStatData> calculateNightNumData(List<BizDataClassStatDailyDO> statTypeData, LocalDate bizDate) {
        // 按统计代码分组
        Map<String, List<BizDataClassStatDailyDO>> groupedByStatCode = statTypeData.stream()
                .collect(Collectors.groupingBy(BizDataClassStatDailyDO::getStatCode));

        List<ClassNightAndOccStatData> result = new ArrayList<>();

        for (Map.Entry<String, List<BizDataClassStatDailyDO>> entry : groupedByStatCode.entrySet()) {
            String statCode = entry.getKey();
            List<BizDataClassStatDailyDO> codeData = entry.getValue();

            ClassNightAndOccStatData nightData = new ClassNightAndOccStatData();
            nightData.setStatCode(statCode);

            // 设置统计名称
            if (!codeData.isEmpty()) {
                nightData.setStatName(codeData.getLast().getStatName());
            }

            // 计算各个时间段的间夜数
            nightData.setTodayNightNumOrOcc(calculatePeriodNightNum(codeData, bizDate, bizDate));
            nightData.setMonthNightNumOrOcc(calculatePeriodNightNum(codeData, bizDate.withDayOfMonth(1), bizDate));
            // 计算去年同期（去年同月累计到相同日期）
            LocalDate lastYearSamePeriod = bizDate.minusYears(1);
            nightData.setLastYearSamePeriodNightNumOrOcc(calculatePeriodNightNum(codeData, lastYearSamePeriod.withDayOfMonth(1), lastYearSamePeriod));
            nightData.setLastMonthNightNumOrOcc(calculatePeriodNightNum(codeData, bizDate.minusMonths(1).withDayOfMonth(1), bizDate.minusMonths(1)));
            nightData.setYearNightNumOrOcc(calculatePeriodNightNum(codeData, bizDate.withDayOfYear(1), bizDate));
            nightData.setLastYearNightNumOrOcc(calculatePeriodNightNum(codeData, bizDate.minusYears(1).withDayOfYear(1), bizDate.minusYears(1)));

            result.add(nightData);
        }

        return result;
    }

    /**
     * 计算出租率数据（基于已计算的间夜数数据）
     */
    private List<ClassNightAndOccStatData> calculateOccData(List<ClassNightAndOccStatData> nightNumDataList, ManagerRespVO managerRespVO) {
        List<ClassNightAndOccStatData> result = new ArrayList<>();

        for (ClassNightAndOccStatData nightData : nightNumDataList) {
            ClassNightAndOccStatData occData = new ClassNightAndOccStatData();
            occData.setStatCode(nightData.getStatCode());
            occData.setStatName(nightData.getStatName());

            // 基于已计算的间夜数计算出租率
            occData.setTodayNightNumOrOcc(NumberUtils.occ(nightData.getTodayNightNumOrOcc(), managerRespVO.getIncomeStat().getTotalRoomNum()));
            occData.setMonthNightNumOrOcc(NumberUtils.occ(nightData.getMonthNightNumOrOcc(), managerRespVO.getMonthIncomeStat().getTotalRoomNum()));
            // 计算去年同期出租率
            occData.setLastYearSamePeriodNightNumOrOcc(NumberUtils.occ(nightData.getLastYearSamePeriodNightNumOrOcc(), managerRespVO.getLastYearSamePeriodIncomeStat().getTotalRoomNum()));
            occData.setLastMonthNightNumOrOcc(NumberUtils.occ(nightData.getLastMonthNightNumOrOcc(), managerRespVO.getLastMonthIncomeStat().getTotalRoomNum()));
            occData.setYearNightNumOrOcc(NumberUtils.occ(nightData.getYearNightNumOrOcc(), managerRespVO.getYearIncomeStat().getTotalRoomNum()));
            occData.setLastYearNightNumOrOcc(NumberUtils.occ(nightData.getLastYearNightNumOrOcc(), managerRespVO.getLastYearIncomeStat().getTotalRoomNum()));

            result.add(occData);
        }

        return result;
    }

    /**
     * 计算房费数据
     */
    private List<ClassFeeStatData> calculateRoomFeeData(List<BizDataClassStatDailyDO> statTypeData, LocalDate bizDate) {
        // 按统计代码分组
        Map<String, List<BizDataClassStatDailyDO>> groupedByStatCode = statTypeData.stream()
                .collect(Collectors.groupingBy(BizDataClassStatDailyDO::getStatCode));

        List<ClassFeeStatData> result = new ArrayList<>();

        for (Map.Entry<String, List<BizDataClassStatDailyDO>> entry : groupedByStatCode.entrySet()) {
            String statCode = entry.getKey();
            List<BizDataClassStatDailyDO> codeData = entry.getValue();

            ClassFeeStatData feeData = new ClassFeeStatData();
            feeData.setStatCode(statCode);

            // 设置统计名称
            if (!codeData.isEmpty()) {
                feeData.setStatName(codeData.getLast().getStatName());
            }

            // 计算各个时间段的房费
            feeData.setTodayRoomFeeOrAvgRoomFee(calculatePeriodRoomFee(codeData, bizDate, bizDate));
            feeData.setMonthRoomFeeOrAvgRoomFee(calculatePeriodRoomFee(codeData, bizDate.withDayOfMonth(1), bizDate));
            // 计算去年同期房费（去年同月累计到相同日期）
            LocalDate lastYearSamePeriod = bizDate.minusYears(1);
            feeData.setLastYearSamePeriodRoomFeeOrAvgRoomFee(calculatePeriodRoomFee(codeData, lastYearSamePeriod.withDayOfMonth(1), lastYearSamePeriod));
            feeData.setLastMonthRoomFeeOrAvgRoomFee(calculatePeriodRoomFee(codeData, bizDate.minusMonths(1).withDayOfMonth(1), bizDate.minusMonths(1)));
            feeData.setYearRoomFeeOrAvgRoomFee(calculatePeriodRoomFee(codeData, bizDate.withDayOfYear(1), bizDate));
            feeData.setLastYearRoomFeeOrAvgRoomFee(calculatePeriodRoomFee(codeData, bizDate.minusYears(1).withDayOfYear(1), bizDate.minusYears(1)));

            result.add(feeData);
        }

        return result;
    }

    /**
     * 计算平均房价数据（重用已计算的间夜数和房费数据）
     */
    private List<ClassFeeStatData> calculateAvgRoomFeeData(List<ClassNightAndOccStatData> nightNumData, List<ClassFeeStatData> roomFeeData) {
        // 创建间夜数数据的映射，便于查找
        Map<String, ClassNightAndOccStatData> nightNumMap = nightNumData.stream()
                .collect(Collectors.toMap(ClassNightAndOccStatData::getStatCode, data -> data));

        // 创建房费数据的映射，便于查找
        Map<String, ClassFeeStatData> roomFeeMap = roomFeeData.stream()
                .collect(Collectors.toMap(ClassFeeStatData::getStatCode, data -> data));

        List<ClassFeeStatData> result = new ArrayList<>();

        // 遍历房费数据，计算对应的平均房价
        for (ClassFeeStatData feeData : roomFeeData) {
            String statCode = feeData.getStatCode();
            ClassNightAndOccStatData nightData = nightNumMap.get(statCode);

            if (nightData == null) {
                continue; // 如果没有对应的间夜数数据，跳过
            }

            ClassFeeStatData avgFeeData = new ClassFeeStatData();
            avgFeeData.setStatCode(statCode);
            avgFeeData.setStatName(feeData.getStatName());

            // 使用已计算的数据计算平均房价
            avgFeeData.setTodayRoomFeeOrAvgRoomFee(NumberUtils.adr(feeData.getTodayRoomFeeOrAvgRoomFee(), nightData.getTodayNightNumOrOcc()));
            avgFeeData.setMonthRoomFeeOrAvgRoomFee(NumberUtils.adr(feeData.getMonthRoomFeeOrAvgRoomFee(), nightData.getMonthNightNumOrOcc()));
            // 计算去年同期平均房价
            avgFeeData.setLastYearSamePeriodRoomFeeOrAvgRoomFee(NumberUtils.adr(feeData.getLastYearSamePeriodRoomFeeOrAvgRoomFee(), nightData.getLastYearSamePeriodNightNumOrOcc()));
            avgFeeData.setLastMonthRoomFeeOrAvgRoomFee(NumberUtils.adr(feeData.getLastMonthRoomFeeOrAvgRoomFee(), nightData.getLastMonthNightNumOrOcc()));
            avgFeeData.setYearRoomFeeOrAvgRoomFee(NumberUtils.adr(feeData.getYearRoomFeeOrAvgRoomFee(), nightData.getYearNightNumOrOcc()));
            avgFeeData.setLastYearRoomFeeOrAvgRoomFee(NumberUtils.adr(feeData.getLastYearRoomFeeOrAvgRoomFee(), nightData.getLastYearNightNumOrOcc()));

            result.add(avgFeeData);
        }

        return result;
    }

    /**
     * 计算指定时间段的间夜数
     */
    private BigDecimal calculatePeriodNightNum(List<BizDataClassStatDailyDO> codeData, LocalDate startDate, LocalDate endDate) {
        return codeData.stream()
                .filter(data -> !data.getBizDate().isBefore(startDate) && !data.getBizDate().isAfter(endDate))
                .map(BizDataClassStatDailyDO::getNightNum)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }



    /**
     * 计算指定时间段的房费
     */
    private Long calculatePeriodRoomFee(List<BizDataClassStatDailyDO> codeData, LocalDate startDate, LocalDate endDate) {
        return codeData.stream()
                .filter(data -> !data.getBizDate().isBefore(startDate) && !data.getBizDate().isAfter(endDate))
                .map(BizDataClassStatDailyDO::getRoomFee)
                .filter(Objects::nonNull)
                .reduce(0L, Long::sum);
    }



    private void calculateMonthYearIncomeStat(List<IncomeStatDailyDO> incomeStatDailyList, List<ManagerDailyDO> managerDailyDOS,
                                              ManagerRespVO managerRespVO, LocalDate bizDate) {
        /*if(CollUtil.isEmpty(incomeStatMonthDOList)){
            return;
        }*/
        // 过滤本月1号到bizDate发生的数据
        List<IncomeStatDailyDO> incomeStatDailyDOS = CollectionUtils.filterList(incomeStatDailyList, incomeStatDailyDO -> incomeStatDailyDO.getBizDate().getYear() == bizDate.getYear()
                && incomeStatDailyDO.getBizDate().getMonth() == bizDate.getMonth()
                && !incomeStatDailyDO.getBizDate().isAfter(bizDate));
        List<ManagerDailyDO> currentMonthManagerDailyDOS = CollectionUtils.filterList(managerDailyDOS, managerDailyDO -> managerDailyDO.getBizDate().getYear() == bizDate.getYear()
                && managerDailyDO.getBizDate().getMonth() == bizDate.getMonth()
                && !managerDailyDO.getBizDate().isAfter(bizDate));

        managerRespVO.setMonthIncomeStat(calculateMonthIncomeStat(incomeStatDailyDOS, currentMonthManagerDailyDOS));
        // 其他费用用减去房费
        //managerRespVO.getMonthIncomeStat().setOtherFee(managerRespVO.getMonthIncomeStat().getOtherFee() - managerRespVO.getMonthIncomeStat().getRoomFee());

        // 过滤去年同期（去年同月累计到相同日期）的数据
        LocalDate lastYearSamePeriod = bizDate.minusYears(1);
        List<IncomeStatDailyDO> lastYearSamePeriodIncomeStatDailyDOS = CollectionUtils.filterList(incomeStatDailyList, incomeStatDailyDO ->
                incomeStatDailyDO.getBizDate().getYear() == lastYearSamePeriod.getYear()
                && incomeStatDailyDO.getBizDate().getMonth() == lastYearSamePeriod.getMonth()
                && !incomeStatDailyDO.getBizDate().isAfter(lastYearSamePeriod));
        List<ManagerDailyDO> lastYearSamePeriodManagerDailyDOS = CollectionUtils.filterList(managerDailyDOS, managerDailyDO ->
                managerDailyDO.getBizDate().getYear() == lastYearSamePeriod.getYear()
                && managerDailyDO.getBizDate().getMonth() == lastYearSamePeriod.getMonth()
                && !managerDailyDO.getBizDate().isAfter(lastYearSamePeriod));

        managerRespVO.setLastYearSamePeriodIncomeStat(calculateMonthIncomeStat(lastYearSamePeriodIncomeStatDailyDOS, lastYearSamePeriodManagerDailyDOS));
        // 其他费用用减去房费
        //managerRespVO.getLastYearSamePeriodIncomeStat().setOtherFee(managerRespVO.getLastYearSamePeriodIncomeStat().getOtherFee() - managerRespVO.getLastYearSamePeriodIncomeStat().getRoomFee());

        // 过滤上月发生的数据
        LocalDate lastMonth = bizDate.minusMonths(1);
        List<IncomeStatDailyDO> lastMonthIncomeStatDailyDOS = CollectionUtils.filterList(incomeStatDailyList, incomeStatDailyDO -> incomeStatDailyDO.getBizDate().getYear() == lastMonth.getYear()
                && incomeStatDailyDO.getBizDate().getMonth() == lastMonth.getMonth()
                && !incomeStatDailyDO.getBizDate().isAfter(lastMonth));
        List<ManagerDailyDO> lastMonthManagerDailyDOS = CollectionUtils.filterList(managerDailyDOS, managerDailyDO -> managerDailyDO.getBizDate().getYear() == lastMonth.getYear()
                && managerDailyDO.getBizDate().getMonth() == lastMonth.getMonth()
                && !managerDailyDO.getBizDate().isAfter(lastMonth));

        managerRespVO.setLastMonthIncomeStat(calculateMonthIncomeStat(lastMonthIncomeStatDailyDOS,  lastMonthManagerDailyDOS));
        // 其他费用用减去房费
        //managerRespVO.getLastMonthIncomeStat().setOtherFee(managerRespVO.getLastMonthIncomeStat().getOtherFee() - managerRespVO.getLastMonthIncomeStat().getRoomFee());


        // 过滤本年发生的数据
        List<IncomeStatDailyDO> currentYearIncomeStatDailyDOS = CollectionUtils.filterList(incomeStatDailyList, incomeStatDailyDO -> incomeStatDailyDO.getBizDate().getYear() == bizDate.getYear() &&
                !incomeStatDailyDO.getBizDate().isAfter(bizDate));
        List<ManagerDailyDO> currentYearManagerDailyDOS = CollectionUtils.filterList(managerDailyDOS, managerDailyDO -> managerDailyDO.getBizDate().getYear() == bizDate.getYear() &&
                !managerDailyDO.getBizDate().isAfter(bizDate));

        managerRespVO.setYearIncomeStat(calculateYearlyIncomeStat(currentYearIncomeStatDailyDOS, currentYearManagerDailyDOS));
        // 其他费用用减去房费
        //managerRespVO.getYearIncomeStat().setOtherFee(managerRespVO.getYearIncomeStat().getOtherFee() - managerRespVO.getYearIncomeStat().getRoomFee());

        // 过滤去年发生的数据
        LocalDate lastYear = bizDate.minusYears(1);
        List<IncomeStatDailyDO> lastYearIncomeStatDailyDOS = CollectionUtils.filterList(incomeStatDailyList, incomeStatDailyDO -> incomeStatDailyDO.getBizDate().getYear() == lastYear.getYear() &&
                !incomeStatDailyDO.getBizDate().isAfter(lastYear));
        List<ManagerDailyDO> lastYearManagerDailyDOS = CollectionUtils.filterList(managerDailyDOS, managerDailyDO -> managerDailyDO.getBizDate().getYear() == lastYear.getYear() &&
                !managerDailyDO.getBizDate().isAfter(lastYear));

        managerRespVO.setLastYearIncomeStat(calculateYearlyIncomeStat(lastYearIncomeStatDailyDOS, lastYearManagerDailyDOS));
        // 其他费用用减去房费
        //managerRespVO.getLastYearIncomeStat().setOtherFee(managerRespVO.getLastYearIncomeStat().getOtherFee() - managerRespVO.getLastYearIncomeStat().getRoomFee());
    }

    private ManagerIncomeRespVO calculateYearlyIncomeStat(List<IncomeStatDailyDO> yearIncomeStatDailyDOS,  List<ManagerDailyDO> yearManagerDailyDOS) {
        ManagerIncomeRespVO managerIncomeRespVO = new ManagerIncomeRespVO();
        // 初始化所有字段为默认值，避免 NullPointerException
        initializeManagerIncomeRespVO(managerIncomeRespVO);

        // 累加多个 ManagerDailyDO 的数据
        aggregateManagerDailyData(managerIncomeRespVO, yearManagerDailyDOS);

        // 计算衍生指标
        calculateDerivedMetrics(managerIncomeRespVO);

        // 处理年度收入统计数据（使用分组累加的方式）
        processYearlyIncomeStatData(managerIncomeRespVO, yearIncomeStatDailyDOS);

        // 计算总费用
        calculateTotalFees(managerIncomeRespVO);

        return managerIncomeRespVO;
    }

    /**
     * 初始化 ManagerIncomeRespVO 对象的所有字段为默认值，避免 NullPointerException
     */
    private void initializeManagerIncomeRespVO(ManagerIncomeRespVO managerIncomeRespVO) {
        // 初始化 BigDecimal 类型字段
        managerIncomeRespVO.setNightNum(BigDecimal.ZERO);
        managerIncomeRespVO.setOcc(BigDecimal.ZERO);
        managerIncomeRespVO.setOvernightOcc(BigDecimal.ZERO);

        // 初始化 Integer 类型字段
        managerIncomeRespVO.setTotalRoomNum(0);
        managerIncomeRespVO.setEmptyNum(0);
        managerIncomeRespVO.setRepairNum(0);
        managerIncomeRespVO.setSelfNum(0);
        managerIncomeRespVO.setOvernightNum(0);
        managerIncomeRespVO.setOpenRoomNum(0);
        managerIncomeRespVO.setWorkInNum(0);
        managerIncomeRespVO.setBookInNum(0);
        managerIncomeRespVO.setCancelBookNum(0);
        managerIncomeRespVO.setNoShowNum(0);
        managerIncomeRespVO.setMemberCardSellNum(0);
        managerIncomeRespVO.setCreditNum(0);
        managerIncomeRespVO.setFreeUpNum(0);

        // 初始化 Long 类型字段
        managerIncomeRespVO.setRoomFee(0L);
        managerIncomeRespVO.setCouponDeduction(0L);
        managerIncomeRespVO.setRevPar(0L);
        managerIncomeRespVO.setAvgRoomFee(0L);
        managerIncomeRespVO.setMemberCardFee(0L);
        managerIncomeRespVO.setGoodFee(0L);
        managerIncomeRespVO.setCateringFee(0L);
        managerIncomeRespVO.setIndemnityFee(0L);
        managerIncomeRespVO.setOtherFee(0L);
        managerIncomeRespVO.setMemberRechargeFee(0L);
        managerIncomeRespVO.setCashFee(0L);
        managerIncomeRespVO.setBankCardFee(0L);
        managerIncomeRespVO.setArFee(0L);
        managerIncomeRespVO.setOnlinePayFee(0L);
        managerIncomeRespVO.setWxPayFee(0L);
        managerIncomeRespVO.setAlipayPayFee(0L);
        managerIncomeRespVO.setStoreCardFee(0L);
        managerIncomeRespVO.setOtherReceiptFee(0L);
    }

    private ManagerIncomeRespVO calculateIncomeStat(List<IncomeStatDailyDO> incomeStatDailyList,
                                                    List<ManagerDailyDO> currentDayManagerDailyDOS) {
        ManagerDailyDO managerDailyDO = new ManagerDailyDO();
        if(CollUtil.isNotEmpty(currentDayManagerDailyDOS)){
            managerDailyDO = currentDayManagerDailyDOS.getFirst();
        }

        ManagerIncomeRespVO managerIncomeRespVO = new ManagerIncomeRespVO();
        // 初始化所有字段为默认值，避免后续处理中的 NullPointerException
        initializeManagerIncomeRespVO(managerIncomeRespVO);

        // 从单个 ManagerDailyDO 设置基础数据
        setManagerDailyDataToResponse(managerIncomeRespVO, managerDailyDO);

        // 处理收入统计数据
        processIncomeStatData(managerIncomeRespVO, incomeStatDailyList);

        // 计算总费用
        calculateTotalFees(managerIncomeRespVO);

        return managerIncomeRespVO;
    }

    private ManagerIncomeRespVO calculateMonthIncomeStat(List<IncomeStatDailyDO> incomeStatDailyDOS, List<ManagerDailyDO> monthManagerDailyDOS) {
        ManagerIncomeRespVO managerIncomeRespVO = new ManagerIncomeRespVO();
        // 初始化所有字段为默认值，避免 NullPointerException
        initializeManagerIncomeRespVO(managerIncomeRespVO);

        // 累加多个 ManagerDailyDO 的数据
        aggregateManagerDailyData(managerIncomeRespVO, monthManagerDailyDOS);

        // 计算衍生指标
        calculateDerivedMetrics(managerIncomeRespVO);

        // 处理收入统计数据
        processIncomeStatData(managerIncomeRespVO, incomeStatDailyDOS);

        // 计算总费用
        calculateTotalFees(managerIncomeRespVO);

        return managerIncomeRespVO;
    }

    /*private ManagerIncomeRespVO calculateIncomeStat(ManagerDailyReqVO reqVO) {

        ManagerIncomeRespVO managerIncomeRespVO = new ManagerIncomeRespVO();

        // 计算本日累计发生并赋值
        List<IncomeStatDailyDO> incomeStatDailyList = incomeStatDailyService.getIncomeStatDailyList(new IncomeStatDailyReqVO()
                .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setStartDate(reqVO.getBizDate()).setEndDate(reqVO.getBizDate()));


        ManagerDailyDO managerDailyDO = managerDailyMapper.selectOne(reqVO);

        managerIncomeRespVO.setNightNum(managerDailyDO.getNightNum());
        managerIncomeRespVO.setTotalRoomNum(managerDailyDO.getTotalRoomNum());
        managerIncomeRespVO.setOcc(managerDailyDO.getOcc());
        managerIncomeRespVO.setRevPar(managerDailyDO.getRevPar());
        managerIncomeRespVO.setAvgRoomFee(managerDailyDO.getAvgRoomFee());
        managerIncomeRespVO.setRoomFee(managerDailyDO.getRoomFee());
        managerIncomeRespVO.setOvernightOcc(managerDailyDO.getOvernightOcc());
        managerIncomeRespVO.setOvernightNum(managerDailyDO.getOvernightNum());
        managerIncomeRespVO.setOpenRoomNum(managerDailyDO.getOpenRoomNum());
        managerIncomeRespVO.setWorkInNum(managerDailyDO.getWorkInNum());
        managerIncomeRespVO.setBookInNum(managerDailyDO.getBookInNum());
        managerIncomeRespVO.setCancelBookNum(managerDailyDO.getCancelBookNum());
        managerIncomeRespVO.setNoShowNum(managerDailyDO.getNoShowNum());
        managerIncomeRespVO.setMemberCardSellNum(managerDailyDO.getMemberCardSellNum());
        managerIncomeRespVO.setCreditNum(managerDailyDO.getCreditNum());
        managerIncomeRespVO.setFreeUpNum(managerDailyDO.getFreeUpNum());
        managerIncomeRespVO.setMemberCardFee(managerDailyDO.getMemberCardFee());

        //根据这两个枚举类中的科目PayAccountEnum;ConsumeAccountEnum,提取incomeStatDailyList中的值
        // 计算完，赋值给ManagerIncomeRespVO
        incomeStatDailyList.forEach(incomeStatDailyDO -> {
            if (DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode().equals(incomeStatDailyDO.getSubType())) {
              *//*  if (ConsumeAccountEnum.ROOM_FEE.getCode().equals(incomeStatDailyDO.getSubCode())) {
                    managerIncomeRespVO.setRoomFee(incomeStatDailyDO.getFee());
                } else if (ConsumeAccountEnum.MEMBER_CARD.getCode().equals(incomeStatDailyDO.getSubCode())) {
                    managerIncomeRespVO.setMemberCardFee(incomeStatDailyDO.getFee());
                }*//* if (ConsumeAccountEnum.GOODS.getCode().equals(incomeStatDailyDO.getSubCode())) {
                    managerIncomeRespVO.setGoodFee(incomeStatDailyDO.getFee());
                } else if (ConsumeAccountEnum.CATERING.getCode().equals(incomeStatDailyDO.getSubCode())) {
                    managerIncomeRespVO.setCateringFee(incomeStatDailyDO.getFee());
                } else if (ConsumeAccountEnum.MEMBER_RECHARGE.getCode().equals(incomeStatDailyDO.getSubCode())) {
                    managerIncomeRespVO.setMemberRechargeFee(incomeStatDailyDO.getFee());
                } else {
                    // 其他消费有很多，要累加
                    if(managerIncomeRespVO.getOtherFee() == null){
                        managerIncomeRespVO.setOtherFee(0L);
                    }
                    managerIncomeRespVO.setOtherFee(managerIncomeRespVO.getOtherFee() + incomeStatDailyDO.getFee());
                }
            } else if (DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode().equals(incomeStatDailyDO.getSubType())) {
                if (PayAccountEnum.RMB_RECEIPT.getCode().equals(incomeStatDailyDO.getSubCode())) {
                    managerIncomeRespVO.setCashFee(incomeStatDailyDO.getFee());
                } else if (PayAccountEnum.CREDIT_S_ACCOUNT.getCode().equals(incomeStatDailyDO.getSubCode())) {
                    managerIncomeRespVO.setArFee(incomeStatDailyDO.getFee());
                }
                else if (PayAccountEnum.BANK_CARD.getCode().equals(incomeStatDailyDO.getSubCode())) {
                    managerIncomeRespVO.setBankCardFee(incomeStatDailyDO.getFee());
                }
                // 微信支付，支付宝支付都属于在线支付
                else if (PayAccountEnum.SCAN_GUN_WX.getCode().equals(incomeStatDailyDO.getSubCode()) || PayAccountEnum.SCAN_GUN_ALIPAY.getCode().equals(incomeStatDailyDO.getSubCode())) {
                    managerIncomeRespVO.setOnlinePayFee(incomeStatDailyDO.getFee());
                } else {
                    // 其他收款有很多，要累加
                    if(managerIncomeRespVO.getOtherReceiptFee() == null){
                        managerIncomeRespVO.setOtherReceiptFee(0L);
                    }
                    managerIncomeRespVO.setOtherReceiptFee(managerIncomeRespVO.getOtherReceiptFee() + incomeStatDailyDO.getFee());
                }
            }
        });
        return managerIncomeRespVO;
    }*/

    /**
     * 构建分类数据
     *
     * @param reqVO
     * @param managerDailyMonthRespVO
     * @param managerMonthRespVOMap
     */
    private void configureClassificationData(ManagerDailyReqVO reqVO, ManagerDailyMonthRespVO managerDailyMonthRespVO, Map<LocalDate, ManagerMonthDetailRespVO> managerMonthRespVOMap) {
        List<MonthBusinessData> totalBusiness = CollUtil.newArrayList();
        // 订单来源分类
        List<MonthBusinessData> orderSrcList = CollUtil.newArrayList();
        List<NameData> orderSrcNameList = CollUtil.newArrayList();
        // 房型分类
        List<MonthBusinessData> rtCodeList = CollUtil.newArrayList();
        List<NameData> rtCodeNameList = CollUtil.newArrayList();
        // 客源分类
        List<MonthBusinessData> gsrcList = CollUtil.newArrayList();
        List<NameData> gsrcNameList = CollUtil.newArrayList();
        Map<String, NameData> gsrcNameMap = CollectionUtils.convertMap(gsrcNameList, NameData::getName);
        // 渠道分类
        List<MonthBusinessData> statChannelList = CollUtil.newArrayList();
        List<NameData> statChannelNameList = CollUtil.newArrayList();
        // 入住类型分类
        List<MonthBusinessData> inTypeList = CollUtil.newArrayList();
        List<NameData> inTypeNameList = CollUtil.newArrayList();
        // 获取房费日报表信息
        RoomRateDailyReportRespVO rateDailyReport = roomRateDailyService.getRateDailyReport(new RoomRateDailyReportReqVO().setHcode(reqVO.getHcode()).setGcode(reqVO.getGcode()).setStartDate(reqVO.getStartDate()).setEndDate(reqVO.getEndDate()));
        List<RoomRateDailyRespVO> totalRateDailyList = rateDailyReport.getList();

        // 使用 TreeMap 替代 HashMap，确保按日期从小到大排序
        Map<LocalDate, List<RoomRateDailyRespVO>> dateRateDailyListMap = new TreeMap<>(
                CollectionUtils.convertMultiMap(totalRateDailyList, RoomRateDailyRespVO::getBizDate)
        );

        for (Map.Entry<LocalDate, List<RoomRateDailyRespVO>> key : dateRateDailyListMap.entrySet()) {
            // 获得没人的房费日报表数据
            List<RoomRateDailyRespVO> rateDailyList = key.getValue();
            LocalDate bizDate = key.getKey();
            ManagerMonthDetailRespVO roomRevenue = managerMonthRespVOMap.getOrDefault(bizDate, new ManagerMonthDetailRespVO());

            List<MonthBusinessData> business = CollUtil.newArrayList();
            // 1. 根据 订单来源分类 转换为BusinessData对象 集合
            if (CollUtil.isEmpty(reqVO.getStatTypeList()) || reqVO.getStatTypeList().contains(StatTypeEnum.ORDER_SOURCE.getCode())) {
                Map<String, List<RoomRateDailyRespVO>> orderSrcMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getOrderSrc);
                Map<String, String> orderSrcNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getOrderSrc, RoomRateDailyRespVO::getOrderSrcName);
                MonthBusinessData orderSrc = classificationData(orderSrcMap, ORDERSRC, bizDate, orderSrcNameMap, orderSrcNameList, roomRevenue);
                //business.add(orderSrc);
                orderSrcList.add(orderSrc);
            }
            // 2. 根据 房型分类 转换为BusinessData对象 集合
            if (CollUtil.isEmpty(reqVO.getStatTypeList()) || reqVO.getStatTypeList().contains(StatTypeEnum.ROOM_TYPE.getCode())) {
                Map<String, List<RoomRateDailyRespVO>> rtcodeMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getRtCode);
                Map<String, String> rtcodeNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getRtCode, RoomRateDailyRespVO::getRtName);
                MonthBusinessData rtCode = classificationData(rtcodeMap, RTCODE, bizDate, rtcodeNameMap, rtCodeNameList, roomRevenue);
                //business.add(rtCode);
                rtCodeList.add(rtCode);
            }
            // 3. 根据 客源分类 转换为BusinessData对象 集合
            if (CollUtil.isEmpty(reqVO.getStatTypeList()) || reqVO.getStatTypeList().contains(StatTypeEnum.GUEST_SRC.getCode())) {
                Map<String, List<RoomRateDailyRespVO>> gSrcMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getGSrc);
                Map<String, String> gSrcNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getGSrc, RoomRateDailyRespVO::getGSrcName);
                MonthBusinessData gsrc = classificationData(gSrcMap, GSRC, bizDate, gSrcNameMap, gsrcNameList, roomRevenue);
                //business.add(gsrc);
                gsrcList.add(gsrc);
            }
            // 4. 根据 渠道分类 转换为BusinessData对象 集合
            if (CollUtil.isEmpty(reqVO.getStatTypeList()) || reqVO.getStatTypeList().contains(StatTypeEnum.CHANNEL.getCode())) {
                Map<String, List<RoomRateDailyRespVO>> statChannelMap = rateDailyList.stream()
                        .filter(roomRate -> roomRate.getStatChannel() != null)
                        .collect(Collectors.groupingBy(RoomRateDailyRespVO::getStatChannel));

                Map<String, String> statChannelNameMap = rateDailyList.stream()
                        .filter(roomRate -> roomRate.getStatChannel() != null && roomRate.getStatChannelName() != null) // 过滤掉 statChannel 为 null 的记录
                        .collect(Collectors.toMap(
                                RoomRateDailyRespVO::getStatChannel,    // 键：statChannel
                                RoomRateDailyRespVO::getStatChannelName, // 值：statChannelName
                                (existing, replacement) -> existing     // 如果有重复键，保留第一个值
                        ));

                MonthBusinessData statChannel = classificationData(statChannelMap, CHANNEL, bizDate, statChannelNameMap, statChannelNameList, roomRevenue);
                //business.add(statChannel);
                statChannelList.add(statChannel);
            }

            // 5. 根据 入住类型分类 转换为BusinessData对象 集合
            if (CollUtil.isEmpty(reqVO.getStatTypeList()) || reqVO.getStatTypeList().contains(StatTypeEnum.CHECK_IN_TYPE.getCode())) {
                Map<String, List<RoomRateDailyRespVO>> inTypeMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getInType);
                Map<String, String> inTypeNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getInType, RoomRateDailyRespVO::getInTypeName);
                MonthBusinessData inType = classificationData(inTypeMap, INTYPE, bizDate, inTypeNameMap, inTypeNameList, roomRevenue);
                //business.add(inType);
                inTypeList.add(inType);
                //totalBusiness.addAll(business);
            }

        }

        //managerDailyMonthRespVO.setGsrcList(gsrcList);
        managerDailyMonthRespVO.setGsrcList(classificationData2(gsrcList, gsrcNameList));
        managerDailyMonthRespVO.setGsrcNameList(gsrcNameList);
        managerDailyMonthRespVO.setRtCodeList(classificationData2(rtCodeList, rtCodeNameList));
        managerDailyMonthRespVO.setRtCodeNameList(rtCodeNameList);
        managerDailyMonthRespVO.setInTypeList(classificationData2(inTypeList, inTypeNameList));
        managerDailyMonthRespVO.setInTypeNameList(inTypeNameList);
        managerDailyMonthRespVO.setOrderSrcList(classificationData2(orderSrcList, orderSrcNameList));
        managerDailyMonthRespVO.setOrderSrcNameList(orderSrcNameList);
        managerDailyMonthRespVO.setStatChannelList(classificationData2(statChannelList, statChannelNameList));
        managerDailyMonthRespVO.setStatChannelNameList(statChannelNameList);

    }

    //  配置对应日期下，没有的数据初始化为0, 同时求和
    private List<MonthBusinessData> classificationData2(List<MonthBusinessData> businessDataList, List<NameData> nameList) {
        //CollectionUtils.convertMap(businessDataList);
        businessDataList.forEach(businessData -> {
            Map<String, ClassificationBusinessData> classificationBusinessDataMap = CollectionUtils.convertMap(businessData.getData(), ClassificationBusinessData::getClassificationStatistics);
            List<ClassificationBusinessData> list = new ArrayList<>();
            nameList.forEach(nameData -> {
                ClassificationBusinessData data = classificationBusinessDataMap.get(nameData.getName());
                if (data != null) {
                    list.add(BeanUtils.toBean(data, ClassificationBusinessData.class));
                    if (nameData.getNightNumSum() == null) {
                        nameData.setNightNumSum(new BigDecimal(0));
                    }
                    if (nameData.getTotalFeeSum() == null) {
                        nameData.setTotalFeeSum(0L);
                    }
                    nameData.setNightNumSum(nameData.getNightNumSum().add(data.getNightNum()));
                    nameData.setTotalFeeSum(nameData.getTotalFeeSum() + data.getTotalFee());
                } else {
                    ClassificationBusinessData classificationBusinessData = new ClassificationBusinessData();
                    classificationBusinessData.setClassificationStatistics(nameData.getName());
                    list.add(classificationBusinessData);
                }
            });
            businessData.setData(list);
        });
        return businessDataList;
    }

    private void configureAccountCount(ManagerDailyReqVO reqVO, List<ManagerMonthDetailRespVO> RoomRevenueList) {
        // 获取交班模式配置
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null && reqVO.getStartDate().isAfter(reqVO.getEndDate())) {
            throw exception(DATE_ERROR);
        }
        // 获得房间账务
        List<AccountRespDTO> accountDOList = accountApi.getPayOrConsumeAccountList(new PayOrConsumeDetailReqDTO()
                .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setStartDate(reqVO.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .setEndDate(reqVO.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).setTimeType(NumberEnum.ONE.getNumber())).getData();
        // 过滤出只有未结的预授权

        // 排除所有预授权类型账务
        List<AccountRespDTO> filteredAccountDOList = accountDOList.stream()
                .filter(account -> !Set.of(
                        PayAccountEnum.BANK_PRE_AUTH.getCode(),
                        PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode()
                ).contains(account.getSubCode()))
                .toList();

        Map<LocalDate, List<AccountRespDTO>> dateAccountListMap = CollectionUtils.convertMultiMap(filteredAccountDOList, AccountRespDTO::getBizDate);
        Map<LocalDate, ManagerMonthDetailRespVO> managerMonthMap = CollectionUtils.convertMap(RoomRevenueList, ManagerMonthDetailRespVO::getBizDate);
        for (Map.Entry<LocalDate, List<AccountRespDTO>> key : dateAccountListMap.entrySet()) {
            List<AccountRespDTO> accountList = key.getValue();
            LocalDate bizDate = key.getKey();
            ManagerMonthDetailRespVO managerMonthDetailRespVO = managerMonthMap.get(bizDate);
            if (managerMonthDetailRespVO == null) {
                continue;
            }
            // 会员卡
            long memberCard = CollectionUtils.filterList(accountList,
                    item -> ConsumeAccountEnum.MEMBER_CARD.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();

            // 小商品
            long good = CollectionUtils.filterList(accountList,
                    item -> ConsumeAccountEnum.GOODS.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();

            // 餐饮
            long catering = CollectionUtils.filterList(accountList,
                    item -> ConsumeAccountEnum.CATERING.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();

            // 赔偿
            long indemnityFee = CollectionUtils.filterList(accountList,
                    item -> ConsumeAccountEnum.INDEMNITY_FEE.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();

            // 会员充值
            long memberRecharge = CollectionUtils.filterList(accountList,
                    item -> ConsumeAccountEnum.MEMBER_RECHARGE.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();

            // 优惠券抵扣
            long couponDeduction = CollectionUtils.filterList(accountList,
                    item -> ConsumeAccountEnum.COUPON_DEDUCTION.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();

            // 消费
            long consume = CollectionUtils.filterList(accountList,
                    item -> DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode().equals(item.getSubType())).stream().mapToLong(AccountRespDTO::getFee).sum();

            // 其他消费
            long otherFee = consume - memberCard - good - catering - indemnityFee - memberRecharge - couponDeduction - managerMonthDetailRespVO.getRoomFee();

            // 门店收入
            long lobbyFee = consume - memberRecharge;

            managerMonthDetailRespVO.setWeek(WeekEnum.getLabelByCode(String.valueOf(bizDate.getDayOfWeek().getValue())));
            managerMonthDetailRespVO.setMemberCard(memberCard);
            managerMonthDetailRespVO.setGood(good);
            managerMonthDetailRespVO.setCatering(catering);
            managerMonthDetailRespVO.setIndemnityFee(indemnityFee);
            managerMonthDetailRespVO.setOtherFee(otherFee);
            managerMonthDetailRespVO.setLobbyFee(lobbyFee);
            managerMonthDetailRespVO.setMemberRecharge(memberRecharge);
            managerMonthDetailRespVO.setCouponDeduction(couponDeduction);
        }

 /*
        List<AccountRespVO> accountRespVOList = BeanUtils.toBean(filteredAccountDOList, AccountRespVO.class);
        // 获取酒店名称
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();


        // 获得科目字典
        List<String> dictTypes = CollUtil.newArrayList(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode());
        List<DictDataRespDTO> dictDataList = dictDataApi.getDictDataListByDicTypes(dictTypes).getData();
        Map<String, DictDataRespDTO> dictDataMap = CollectionUtils.convertMap(dictDataList, DictDataRespDTO::getCode);

        // 填充科目名称
        List<AccountRespVO> accountVoList = CollectionUtils.convertList(accountRespVOList, account -> {
            DictDataRespDTO dictData = dictDataMap.get(account.getSubCode());
            if (dictData != null) {
                account.setSubName(dictData.getLabel());
            }
            return account;
        });
        // 获得班次列表 建立班次名称映射
        List<ShiftTimeDO> shiftTimeList = shiftTimeService.getShiftTimeList(new ShiftTimeReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setState(NumberEnum.ONE.getNumber()));
        Map<String, String> shiftMap = CollectionUtils.convertMap(shiftTimeList, ShiftTimeDO::getShiftCode, ShiftTimeDO::getShiftName);

        // 获取现付账套列表 建立 账套代码与现付账套关系映射  科目代码与现付账套关系映射
        List<AccSetRespVO> accSetList = accSetService.getAccSetList(new AccSetReqVO().setGcode(reqVO.getGcode()).setIsEnable(NumberEnum.ONE.getNumber()));
        Map<String, AccSetRespVO> accSetAccCodeMap = CollectionUtils.convertMap(accSetList, AccSetRespVO::getAccCode);
        Map<String, AccSetRespVO> accSetSubCodeMap = CollectionUtils.convertMap(accSetList, AccSetRespVO::getSubCode);

        // 转换交班报表账务
        List<HandoverReportAccountRespVO> accounts = BeanUtils.toBean(accountVoList, HandoverReportAccountRespVO.class);*/
    }

    private RevenueCount getRevenueCount(ManagerDailyReqVO reqVO) {
        // 获取消费付款信息
        HandoverReportRespDTO handoverReport = accountApi.handoverReport(new HandoverReportReqDTO().setHcode(reqVO.getHcode()).setGcode(reqVO.getGcode()).setBizDate(reqVO.getBizDate()).setOperator(reqVO.getOperator())).getData();
        List<HandoverReportRespDTO.Detail> consumptionDetails = handoverReport.getConsumptionDetails();
        List<HandoverReportRespDTO.Detail> consumptionDetails2 = CollectionUtils.filterList(consumptionDetails, item -> !ConsumeAccountEnum.MEMBER_RECHARGE.getCode().equals(item.getSubCode()));
        List<HandoverReportRespDTO.Detail> memberRechargeConsumptionDetails = CollectionUtils.filterList(consumptionDetails, item -> ConsumeAccountEnum.MEMBER_RECHARGE.getCode().equals(item.getSubCode()));
        Long memberRechargeFee = 0L;
        if (ObjectUtil.isNotEmpty(memberRechargeConsumptionDetails)) {
            memberRechargeFee = memberRechargeConsumptionDetails.stream().mapToLong(HandoverReportRespDTO.Detail::getTotalFee).sum();
        }
        List<HandoverReportRespDTO.Detail> payDetails = handoverReport.getPaymentDetails();
        // 获取宾客账信息
        GuestDetailReportRespVO guestDetailReport = guestDetailService.getGuestDetailReport(new GuestDetailReportReqVO().setHcode(reqVO.getHcode()).setGcode(reqVO.getGcode()).setBizDate(reqVO.getBizDate()).setOperator(reqVO.getOperator()));
        long customerBillTotalFee = guestDetailReport.getList().stream().mapToLong(GuestDetailRespVO::getCustomerBill).sum();
        RevenueCount revenueCount = new RevenueCount();
        revenueCount.setConsumptionDetails(BeanUtils.toBean(consumptionDetails2, RevenueCount.Detail.class))
                .setPaymentDetails(BeanUtils.toBean(payDetails, RevenueCount.Detail.class))
                .setMemberRechargeTotalFee(handoverReport.getMemberRechargeTotalFee())
                .setConsumptionTypeTotalFee(handoverReport.getConsumptionTypeTotalFee())
                .setPaymentTypeTotalFee(handoverReport.getPaymentTypeTotalFee())
                .setGMemberRechargeTotalFee(memberRechargeFee)
                .setMMemberRechargeTotalFee(handoverReport.getMMemberRechargeTotalFee())
                .setCustomerBillTotalFee(customerBillTotalFee);
        return revenueCount;
    }

    @NotNull
    private List<BusinessData> getBusinessData(ManagerDailyReqVO reqVO) {
        List<BusinessData> business = CollUtil.newArrayList();
        // 获取房费日报表信息
        RoomRateDailyReportRespVO rateDailyReport = roomRateDailyService.getRateDailyReport(new RoomRateDailyReportReqVO().setHcode(reqVO.getHcode()).setGcode(reqVO.getGcode()).setBizDate(reqVO.getBizDate()).setOperator(reqVO.getOperator()));
        List<RoomRateDailyRespVO> rateDailyList = rateDailyReport.getList();

        // 1. 根据 订单来源分类 转换为BusinessData对象 集合
        Map<String, List<RoomRateDailyRespVO>> orderSrcMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getOrderSrc);
        Map<String, String> orderSrcNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getOrderSrc, RoomRateDailyRespVO::getOrderSrcName);
        List<BusinessData> orderSrc = classification(orderSrcMap, ORDERSRC, orderSrcNameMap);
        business.addAll(orderSrc);
        // 2. 根据 房型分类 转换为BusinessData对象 集合
        Map<String, List<RoomRateDailyRespVO>> rtcodeMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getRtCode);
        Map<String, String> rtcodeNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getRtCode, RoomRateDailyRespVO::getRtName);
        List<BusinessData> rtCode = classification(rtcodeMap, RTCODE, rtcodeNameMap);
        business.addAll(rtCode);
        // 3. 根据 客源分类 转换为BusinessData对象 集合
        Map<String, List<RoomRateDailyRespVO>> gSrcMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getGSrc);
        Map<String, String> gSrcNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getGSrc, RoomRateDailyRespVO::getGSrcName);
        List<BusinessData> gsrc = classification(gSrcMap, GSRC, gSrcNameMap);
        business.addAll(gsrc);
        // 4. 根据 渠道分类 转换为BusinessData对象 集合
        Map<String, List<RoomRateDailyRespVO>> statChannelMap = rateDailyList.stream()
                .filter(roomRate -> roomRate.getStatChannel() != null)
                .collect(Collectors.groupingBy(RoomRateDailyRespVO::getStatChannel));

        Map<String, String> statChannelNameMap = rateDailyList.stream()
                .filter(roomRate -> roomRate.getStatChannel() != null && roomRate.getStatChannelName() != null) // 过滤掉 statChannel 为 null 的记录
                .collect(Collectors.toMap(
                        RoomRateDailyRespVO::getStatChannel,    // 键：statChannel
                        RoomRateDailyRespVO::getStatChannelName, // 值：statChannelName
                        (existing, replacement) -> existing     // 如果有重复键，保留第一个值
                ));

        List<BusinessData> statChannel = classification(statChannelMap, CHANNEL, statChannelNameMap);
        business.addAll(statChannel);
        // 5. 根据 入住类型分类 转换为BusinessData对象 集合
        Map<String, List<RoomRateDailyRespVO>> inTypeMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getInType);
        Map<String, String> inTypeNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getInType, RoomRateDailyRespVO::getInTypeName);
        List<BusinessData> inType = classification(inTypeMap, INTYPE, inTypeNameMap);
        business.addAll(inType);
        return business;
    }

    private List<BusinessData> classification(Map<String, List<RoomRateDailyRespVO>> listMap, String category, Map<String, String> nameMap) {
        return listMap.entrySet().stream().map(entry -> {
            List<RoomRateDailyRespVO> list = entry.getValue();
            // 计算总间夜数和总费用
            BigDecimal totalNightNum = list.stream()
                    .map(RoomRateDailyRespVO::getNightNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            long totalFee = list.stream().mapToLong(RoomRateDailyRespVO::getFee).sum();
            // 创建BusinessData对象
            BusinessData businessData = new BusinessData();
            businessData.setCateGory(category);
            businessData.setClassificationStatistics(nameMap.get(entry.getKey()));
            businessData.setNightNum(totalNightNum);
            businessData.setTotalFee(totalFee);
            businessData.setBizDate(list.getFirst().getBizDate());
            return businessData;
        }).collect(Collectors.toList());
    }

    /**
     * 构建数据
     *
     * @param listMap
     * @param category    分类名称
     * @param bizDate
     * @param nameMap     对应日期下的数据子分类名称map
     * @param nameList    所有日期下的子分类名称集合
     * @param roomRevenue 对应日期的经营数据
     * @return
     */
    private MonthBusinessData classificationData(Map<String, List<RoomRateDailyRespVO>> listMap, String category, LocalDate bizDate,
                                                 Map<String, String> nameMap, List<NameData> nameList, ManagerMonthDetailRespVO roomRevenue) {
        MonthBusinessData monthBusinessData = new MonthBusinessData();
        List<ClassificationBusinessData> businessDataList = new ArrayList<>();
        monthBusinessData.setCateGory(category);
        monthBusinessData.setBizDate(bizDate);
        monthBusinessData.setWeek(WeekEnum.getLabelByCode(String.valueOf(bizDate.getDayOfWeek().getValue())));
        /*for (Map.Entry<String, String> entry : nameMap.entrySet()) {
            // 如果 nameList 中没有与 classificationStatistics 相同的 name，则添加到 nameList
            if (nameList.stream().noneMatch(nameData -> nameData.getName().equals(entry.getValue()))) {
                NameData nameData = new NameData();
                nameData.setName(entry.getValue());
                nameList.add(nameData);
            }
            NameData nameData = new NameData();
            nameData.setName(entry.getValue());
        }
        for (NameData nameData : nameList) {
            ClassificationBusinessData businessData = new ClassificationBusinessData();
            businessData.setClassificationStatistics(nameData.getName());
            businessDataList.add(businessData);
        }
        Map<String, ClassificationBusinessData> businessDataMap = CollectionUtils.convertMap(businessDataList, ClassificationBusinessData::getClassificationStatistics);

        for (Map.Entry<String, List<RoomRateDailyRespVO>> entry : listMap.entrySet()) {
            List<RoomRateDailyRespVO> list = entry.getValue();
            // 计算总间夜数和总费用
            BigDecimal totalNightNum = list.stream()
                    .map(RoomRateDailyRespVO::getNightNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            long totalFee = list.stream().mapToLong(RoomRateDailyRespVO::getFee).sum();
            // 创建BusinessData对象
            //ClassificationBusinessData businessData = new ClassificationBusinessData();
            ClassificationBusinessData businessData = businessDataMap.get(nameMap.get(entry.getKey()));
            //businessData.setClassificationStatistics(nameMap.get(entry.getKey()));
            businessData.setNightNum(totalNightNum);
            businessData.setTotalFee(totalFee);
        }*/
        List<ClassificationBusinessData> collect = listMap.entrySet().stream().map(entry -> {
            List<RoomRateDailyRespVO> list = entry.getValue();
            // 计算总间夜数和总费用
            BigDecimal totalNightNum = list.stream()
                    .map(RoomRateDailyRespVO::getNightNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            long totalFee = list.stream().mapToLong(RoomRateDailyRespVO::getFee).sum();
            BigDecimal occ = NumberUtils.occ(totalNightNum, roomRevenue.getTotalRoomNum());
            // 创建BusinessData对象
            ClassificationBusinessData businessData = new ClassificationBusinessData();
            businessData.setClassificationStatistics(nameMap.get(entry.getKey()));
            if (businessData.getClassificationStatistics() == null) {
                businessData.setClassificationStatistics(entry.getKey());
            }
            businessData.setNightNum(totalNightNum);
            businessData.setTotalFee(totalFee);
            businessData.setOcc(occ);
            if (nameList.stream().noneMatch(nameData -> businessData.getClassificationStatistics().equals(nameData.getName()))) {
                NameData nameData = new NameData();
                nameData.setName(businessData.getClassificationStatistics());
                nameList.add(nameData);
            }
            return businessData;
        }).toList();
        monthBusinessData.setData(collect);
        return monthBusinessData;
    }

    /**
     * 从单个 ManagerDailyDO 设置基础数据到响应对象
     */
    private void setManagerDailyDataToResponse(ManagerIncomeRespVO managerIncomeRespVO, ManagerDailyDO managerDailyDO) {
        if (managerDailyDO.getNightNum() != null) {
            managerIncomeRespVO.setNightNum(managerDailyDO.getNightNum());
        }
        if (managerDailyDO.getTotalRoomNum() != null) {
            managerIncomeRespVO.setTotalRoomNum(managerDailyDO.getTotalRoomNum());
        }
        if (managerDailyDO.getEmptyNum() != null) {
            managerIncomeRespVO.setEmptyNum(managerDailyDO.getEmptyNum());
        }
        if (managerDailyDO.getSelfNum() != null) {
            managerIncomeRespVO.setSelfNum(managerDailyDO.getSelfNum());
        }
        if (managerDailyDO.getRepairNum() != null) {
            managerIncomeRespVO.setRepairNum(managerDailyDO.getRepairNum());
        }
        if (managerDailyDO.getOcc() != null) {
            managerIncomeRespVO.setOcc(managerDailyDO.getOcc());
        }
        if (managerDailyDO.getRevPar() != null) {
            managerIncomeRespVO.setRevPar(managerDailyDO.getRevPar());
        }
        if (managerDailyDO.getAvgRoomFee() != null) {
            managerIncomeRespVO.setAvgRoomFee(managerDailyDO.getAvgRoomFee());
        }
        if (managerDailyDO.getRoomFee() != null) {
            managerIncomeRespVO.setRoomFee(managerDailyDO.getRoomFee());
        }
        if (managerDailyDO.getOvernightOcc() != null) {
            managerIncomeRespVO.setOvernightOcc(managerDailyDO.getOvernightOcc());
        }
        if (managerDailyDO.getOvernightNum() != null) {
            managerIncomeRespVO.setOvernightNum(managerDailyDO.getOvernightNum());
        }
        if (managerDailyDO.getOpenRoomNum() != null) {
            managerIncomeRespVO.setOpenRoomNum(managerDailyDO.getOpenRoomNum());
        }
        if (managerDailyDO.getWorkInNum() != null) {
            managerIncomeRespVO.setWorkInNum(managerDailyDO.getWorkInNum());
        }
        if (managerDailyDO.getBookInNum() != null) {
            managerIncomeRespVO.setBookInNum(managerDailyDO.getBookInNum());
        }
        if (managerDailyDO.getCancelBookNum() != null) {
            managerIncomeRespVO.setCancelBookNum(managerDailyDO.getCancelBookNum());
        }
        if (managerDailyDO.getNoShowNum() != null) {
            managerIncomeRespVO.setNoShowNum(managerDailyDO.getNoShowNum());
        }
        if (managerDailyDO.getMemberCardSellNum() != null) {
            managerIncomeRespVO.setMemberCardSellNum(managerDailyDO.getMemberCardSellNum());
        }
        if (managerDailyDO.getCreditNum() != null) {
            managerIncomeRespVO.setCreditNum(managerDailyDO.getCreditNum());
        }
        if (managerDailyDO.getFreeUpNum() != null) {
            managerIncomeRespVO.setFreeUpNum(managerDailyDO.getFreeUpNum());
        }
        if (managerDailyDO.getMemberCardFee() != null) {
            managerIncomeRespVO.setMemberCardFee(managerDailyDO.getMemberCardFee());
        }
    }

    /**
     * 累加多个 ManagerDailyDO 的数据到响应对象
     */
    private void aggregateManagerDailyData(ManagerIncomeRespVO managerIncomeRespVO, List<ManagerDailyDO> managerDailyDOS) {
        managerDailyDOS.forEach(managerDailyDO -> {
            // 安全累加 BigDecimal 类型字段
            if (managerDailyDO.getNightNum() != null) {
                managerIncomeRespVO.setNightNum(managerIncomeRespVO.getNightNum().add(managerDailyDO.getNightNum()));
            }
            // 安全累加 Integer 和 Long 类型字段
            if (managerDailyDO.getTotalRoomNum() != null) {
                managerIncomeRespVO.setTotalRoomNum(managerIncomeRespVO.getTotalRoomNum() + managerDailyDO.getTotalRoomNum());
            }
            if (managerDailyDO.getEmptyNum() != null) {
                managerIncomeRespVO.setEmptyNum(managerIncomeRespVO.getEmptyNum() + managerDailyDO.getEmptyNum());
            }
            if (managerDailyDO.getRepairNum() != null) {
                managerIncomeRespVO.setRepairNum(managerIncomeRespVO.getRepairNum() + managerDailyDO.getRepairNum());
            }
            if (managerDailyDO.getSelfNum() != null) {
                managerIncomeRespVO.setSelfNum(managerIncomeRespVO.getSelfNum() + managerDailyDO.getSelfNum());
            }
            if (managerDailyDO.getOvernightNum() != null) {
                managerIncomeRespVO.setOvernightNum(managerIncomeRespVO.getOvernightNum() + managerDailyDO.getOvernightNum());
            }
            if (managerDailyDO.getRoomFee() != null) {
                managerIncomeRespVO.setRoomFee(managerIncomeRespVO.getRoomFee() + managerDailyDO.getRoomFee());
            }
            if (managerDailyDO.getOpenRoomNum() != null) {
                managerIncomeRespVO.setOpenRoomNum(managerIncomeRespVO.getOpenRoomNum() + managerDailyDO.getOpenRoomNum());
            }
            if (managerDailyDO.getWorkInNum() != null) {
                managerIncomeRespVO.setWorkInNum(managerIncomeRespVO.getWorkInNum() + managerDailyDO.getWorkInNum());
            }
            if (managerDailyDO.getBookInNum() != null) {
                managerIncomeRespVO.setBookInNum(managerIncomeRespVO.getBookInNum() + managerDailyDO.getBookInNum());
            }
            if (managerDailyDO.getCancelBookNum() != null) {
                managerIncomeRespVO.setCancelBookNum(managerIncomeRespVO.getCancelBookNum() + managerDailyDO.getCancelBookNum());
            }
            if (managerDailyDO.getNoShowNum() != null) {
                managerIncomeRespVO.setNoShowNum(managerIncomeRespVO.getNoShowNum() + managerDailyDO.getNoShowNum());
            }
            if (managerDailyDO.getMemberCardSellNum() != null) {
                managerIncomeRespVO.setMemberCardSellNum(managerIncomeRespVO.getMemberCardSellNum() + managerDailyDO.getMemberCardSellNum());
            }
            if (managerDailyDO.getCreditNum() != null) {
                managerIncomeRespVO.setCreditNum(managerIncomeRespVO.getCreditNum() + managerDailyDO.getCreditNum());
            }
            if (managerDailyDO.getFreeUpNum() != null) {
                managerIncomeRespVO.setFreeUpNum(managerIncomeRespVO.getFreeUpNum() + managerDailyDO.getFreeUpNum());
            }
            if (managerDailyDO.getMemberCardFee() != null) {
                managerIncomeRespVO.setMemberCardFee(managerIncomeRespVO.getMemberCardFee() + managerDailyDO.getMemberCardFee());
            }
        });
    }

    /**
     * 计算衍生指标（出租率、RevPAR、平均房价、过夜出租率）
     */
    private void calculateDerivedMetrics(ManagerIncomeRespVO managerIncomeRespVO) {
        managerIncomeRespVO.setOcc(NumberUtils.occ(managerIncomeRespVO.getNightNum(), managerIncomeRespVO.getTotalRoomNum()));
        managerIncomeRespVO.setRevPar(NumberUtils.revPar(managerIncomeRespVO.getRoomFee(), managerIncomeRespVO.getTotalRoomNum()));
        managerIncomeRespVO.setAvgRoomFee(NumberUtils.adr(managerIncomeRespVO.getRoomFee(), managerIncomeRespVO.getNightNum()));
        managerIncomeRespVO.setOvernightOcc(NumberUtils.occ(new BigDecimal(managerIncomeRespVO.getOvernightNum()), managerIncomeRespVO.getTotalRoomNum()));
    }

    /**
     * 处理收入统计数据（消费账目和付款账目）- 使用累加方式
     */
    private void processIncomeStatData(ManagerIncomeRespVO managerIncomeRespVO, List<IncomeStatDailyDO> incomeStatDailyList) {
        incomeStatDailyList.forEach(incomeStatDailyDO -> {
            if (DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode().equals(incomeStatDailyDO.getSubType())) {
                processConsumeAccountWithAccumulation(managerIncomeRespVO, incomeStatDailyDO);
            } else if (DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode().equals(incomeStatDailyDO.getSubType())) {
                processPayAccountWithAccumulation(managerIncomeRespVO, incomeStatDailyDO);
            }
        });
    }

    /**
     * 处理消费账目（累加方式）
     */
    private void processConsumeAccountWithAccumulation(ManagerIncomeRespVO managerIncomeRespVO, IncomeStatDailyDO incomeStatDailyDO) {
        if (ConsumeAccountEnum.GOODS.getCode().equals(incomeStatDailyDO.getSubCode())) {
            managerIncomeRespVO.setGoodFee(managerIncomeRespVO.getGoodFee() + incomeStatDailyDO.getFee());
        } else if (ConsumeAccountEnum.CATERING.getCode().equals(incomeStatDailyDO.getSubCode())) {
            managerIncomeRespVO.setCateringFee(managerIncomeRespVO.getCateringFee() + incomeStatDailyDO.getFee());
        } else if (ConsumeAccountEnum.INDEMNITY_FEE.getCode().equals(incomeStatDailyDO.getSubCode())) {
            managerIncomeRespVO.setIndemnityFee(managerIncomeRespVO.getIndemnityFee() + incomeStatDailyDO.getFee());
        } else if (ConsumeAccountEnum.MEMBER_RECHARGE.getCode().equals(incomeStatDailyDO.getSubCode())) {
            managerIncomeRespVO.setMemberRechargeFee(managerIncomeRespVO.getMemberRechargeFee() + incomeStatDailyDO.getFee());
        }  else if (ConsumeAccountEnum.COUPON_DEDUCTION.getCode().equals(incomeStatDailyDO.getSubCode())) {
            managerIncomeRespVO.setCouponDeduction(managerIncomeRespVO.getCouponDeduction() + incomeStatDailyDO.getFee());
        }
        else {
            // 其他消费有很多，要累加
            managerIncomeRespVO.setOtherFee(managerIncomeRespVO.getOtherFee() + incomeStatDailyDO.getFee());
        }
    }

    /**
     * 处理付款账目（累加方式）
     */
    private void processPayAccountWithAccumulation(ManagerIncomeRespVO managerIncomeRespVO, IncomeStatDailyDO incomeStatDailyDO) {
        if (PayAccountEnum.RMB_RECEIPT.getCode().equals(incomeStatDailyDO.getSubCode()) || PayAccountEnum.CASH_REFUND.getCode().equals(incomeStatDailyDO.getSubCode())) {
            managerIncomeRespVO.setCashFee(managerIncomeRespVO.getCashFee() + incomeStatDailyDO.getFee());
        } else if (PayAccountEnum.CREDIT_S_ACCOUNT.getCode().equals(incomeStatDailyDO.getSubCode()) || PayAccountEnum.AR_REFUND.getCode().equals(incomeStatDailyDO.getSubCode())) {
            managerIncomeRespVO.setArFee(managerIncomeRespVO.getArFee() + incomeStatDailyDO.getFee());
        } else if (PayAccountEnum.BANK_CARD.getCode().equals(incomeStatDailyDO.getSubCode()) || PayAccountEnum.BANK_CARD_REFUND.getCode().equals(incomeStatDailyDO.getSubCode())) {
            managerIncomeRespVO.setBankCardFee(managerIncomeRespVO.getBankCardFee() + incomeStatDailyDO.getFee());
        } else if (PayAccountEnum.SCAN_GUN_WX.getCode().equals(incomeStatDailyDO.getSubCode()) || PayAccountEnum.WX.getCode().equals(incomeStatDailyDO.getSubCode())
                || PayAccountEnum.SCAN_GUN_WX_REFUND.getCode().equals(incomeStatDailyDO.getSubCode()) || PayAccountEnum.WX_REFUND.getCode().equals(incomeStatDailyDO.getSubCode())) {
            managerIncomeRespVO.setWxPayFee(managerIncomeRespVO.getWxPayFee() + incomeStatDailyDO.getFee());
        } else if (PayAccountEnum.SCAN_GUN_ALIPAY.getCode().equals(incomeStatDailyDO.getSubCode()) || PayAccountEnum.ALIPAY.getCode().equals(incomeStatDailyDO.getSubCode())
                || PayAccountEnum.SCAN_GUN_ALIPAY_REFUND.getCode().equals(incomeStatDailyDO.getSubCode()) || PayAccountEnum.ALIPAY_REFUND.getCode().equals(incomeStatDailyDO.getSubCode())) {
            managerIncomeRespVO.setAlipayPayFee(managerIncomeRespVO.getAlipayPayFee() + incomeStatDailyDO.getFee());
        }else if(PayAccountEnum.STORE_CARD.getCode().equals(incomeStatDailyDO.getSubCode()) || PayAccountEnum.STORE_CARD_REFUND.getCode().equals(incomeStatDailyDO.getSubCode())){
            managerIncomeRespVO.setStoreCardFee(managerIncomeRespVO.getStoreCardFee() + incomeStatDailyDO.getFee());
        } else {
            // 其他收款有很多，要累加
            managerIncomeRespVO.setOtherReceiptFee(managerIncomeRespVO.getOtherReceiptFee() + incomeStatDailyDO.getFee());
        }
    }

    /**
     * 计算总费用（消费总费用和收款总费用）
     */
    private void calculateTotalFees(ManagerIncomeRespVO managerIncomeRespVO) {
        managerIncomeRespVO.setConsumeTotalFee(managerIncomeRespVO.getRoomFee() + managerIncomeRespVO.getCouponDeduction() + managerIncomeRespVO.getMemberCardFee() +
                managerIncomeRespVO.getGoodFee() + managerIncomeRespVO.getCateringFee() + managerIncomeRespVO.getIndemnityFee() + managerIncomeRespVO.getOtherFee());
        managerIncomeRespVO.setReceiptTotalFee(managerIncomeRespVO.getCashFee() + managerIncomeRespVO.getBankCardFee() +
                managerIncomeRespVO.getArFee() + managerIncomeRespVO.getWxPayFee() + managerIncomeRespVO.getAlipayPayFee() + managerIncomeRespVO.getStoreCardFee() +
                managerIncomeRespVO.getOtherReceiptFee());
    }

    /**
     * 处理年度收入统计数据（使用分组累加的方式，适用于大量数据的累加场景）
     */
    private void processYearlyIncomeStatData(ManagerIncomeRespVO managerIncomeRespVO, List<IncomeStatDailyDO> yearIncomeStatDailyDOS) {
        // 分别处理消费类型和付款类型
        List<IncomeStatDailyDO> consumeIncomeStatDailyDOList = CollectionUtils.filterList(yearIncomeStatDailyDOS,
                incomeStatDailyDO -> DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode().equals(incomeStatDailyDO.getSubType()));
        List<IncomeStatDailyDO> paymentIncomeStatDailyDOList = CollectionUtils.filterList(yearIncomeStatDailyDOS,
                incomeStatDailyDO -> DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode().equals(incomeStatDailyDO.getSubType()));

        // 按科目分组并累加费用
        Map<String, Long> consumeSubCodeMap = consumeIncomeStatDailyDOList.stream()
                .collect(Collectors.groupingBy(IncomeStatDailyDO::getSubCode, Collectors.summingLong(IncomeStatDailyDO::getFee)));
        Map<String, Long> paymentSubCodeMap = paymentIncomeStatDailyDOList.stream()
                .collect(Collectors.groupingBy(IncomeStatDailyDO::getSubCode, Collectors.summingLong(IncomeStatDailyDO::getFee)));

        // 处理消费账目
        processYearlyConsumeAccounts(managerIncomeRespVO, consumeSubCodeMap);

        // 处理付款账目
        processYearlyPayAccounts(managerIncomeRespVO, paymentSubCodeMap);
    }

    /**
     * 处理年度消费账目（直接赋值方式，因为已经分组累加过了）
     */
    private void processYearlyConsumeAccounts(ManagerIncomeRespVO managerIncomeRespVO, Map<String, Long> consumeSubCodeMap) {
        consumeSubCodeMap.forEach((subCode, fee) -> {
            if (ConsumeAccountEnum.GOODS.getCode().equals(subCode)) {
                managerIncomeRespVO.setGoodFee(fee);
            } else if (ConsumeAccountEnum.CATERING.getCode().equals(subCode)) {
                managerIncomeRespVO.setCateringFee(fee);
            } else if (ConsumeAccountEnum.INDEMNITY_FEE.getCode().equals(subCode)) {
                managerIncomeRespVO.setIndemnityFee(fee);
            } else if (ConsumeAccountEnum.MEMBER_RECHARGE.getCode().equals(subCode)) {
                managerIncomeRespVO.setMemberRechargeFee(fee);
            } else if (ConsumeAccountEnum.COUPON_DEDUCTION.getCode().equals(subCode)) {
                managerIncomeRespVO.setCouponDeduction(fee);
            }
            else {
                // 其他消费有很多，要累加
                managerIncomeRespVO.setOtherFee(managerIncomeRespVO.getOtherFee() + fee);
            }
        });
    }

    /**
     * 处理年度付款账目（直接赋值方式，因为已经分组累加过了）
     */
    private void processYearlyPayAccounts(ManagerIncomeRespVO managerIncomeRespVO, Map<String, Long> paymentSubCodeMap) {
        paymentSubCodeMap.forEach((subCode, fee) -> {
            if (PayAccountEnum.RMB_RECEIPT.getCode().equals(subCode) || PayAccountEnum.CASH_REFUND.getCode().equals(subCode)) {
                managerIncomeRespVO.setCashFee(fee);
            } else if (PayAccountEnum.BANK_CARD.getCode().equals(subCode) || PayAccountEnum.BANK_CARD_REFUND.getCode().equals(subCode)) {
                managerIncomeRespVO.setBankCardFee(fee);
            } else if (PayAccountEnum.CREDIT_S_ACCOUNT.getCode().equals(subCode) || PayAccountEnum.AR_REFUND.getCode().equals(subCode)) {
                managerIncomeRespVO.setArFee(fee);
            } else if (PayAccountEnum.SCAN_GUN_WX.getCode().equals(subCode) || PayAccountEnum.WX.getCode().equals(subCode)
             || PayAccountEnum.SCAN_GUN_WX_REFUND.getCode().equals(subCode) || PayAccountEnum.WX_REFUND.getCode().equals(subCode)) {
                managerIncomeRespVO.setWxPayFee(fee);
            } else if (PayAccountEnum.SCAN_GUN_ALIPAY.getCode().equals(subCode) || PayAccountEnum.ALIPAY.getCode().equals(subCode)
             || PayAccountEnum.SCAN_GUN_ALIPAY_REFUND.getCode().equals(subCode) || PayAccountEnum.ALIPAY_REFUND.getCode().equals(subCode)) {
                managerIncomeRespVO.setAlipayPayFee(fee);
            } else if (PayAccountEnum.STORE_CARD.getCode().equals(subCode) || PayAccountEnum.STORE_CARD_REFUND.getCode().equals(subCode)) {
                managerIncomeRespVO.setStoreCardFee(fee);
            } else {
                // 其他收款有很多，要累加
                managerIncomeRespVO.setOtherReceiptFee(managerIncomeRespVO.getOtherReceiptFee() + fee);
            }
        });
    }

    @Override
    public BusinessDailyReportVO generateBusinessDailyReport(String gcode, String hcode, LocalDate bizDate) {
        List<ManagerDailyDO> roomData = managerDailyMapper.selectYearList(new  ManagerDailyReqVO().setGcode(gcode).setHcode(hcode).setBizDate(bizDate));
        List<IncomeStatDailyDO> incomeData = incomeStatDailyService.getIncomeStatDailyListByYear(new IncomeStatDailyReqVO().setGcode(gcode).setHcode(hcode).setStartDate(bizDate).setEndDate(bizDate));
        return generateReport(bizDate, incomeData, roomData);
    }


    /**
     * 生成营业日报表
     * @param bizDate 营业日期(如2025-07-17)
     * @param incomeData 全量收入数据(2024-01-01至2025-07-17)
     * @param roomData 全量客房数据(2024-01-01至2025-07-17)
     */
    private BusinessDailyReportVO generateReport(LocalDate bizDate,
                                                 List<IncomeStatDailyDO> incomeData,
                                                 List<ManagerDailyDO> roomData) {
        // 1. 准备日期范围
        LocalDate monthStart = bizDate.withDayOfMonth(1); // 获取当月的第一天
        LocalDate yearStart = bizDate.withDayOfYear(1); // 获取当年的第一天
        LocalDate lastYearDate = bizDate.minusYears(1); // 获取去年同期的日期
        LocalDate lastYearMonthStart = lastYearDate.withDayOfMonth(1); // 获取去年当月的第一天
        LocalDate lastYearYearStart = lastYearDate.withDayOfYear(1); // 获取去年的第一天

        // 2. 构建报表DTO
        BusinessDailyReportVO report = new BusinessDailyReportVO();
        report.setBizDate(bizDate);
        report.setPrintDate(LocalDate.now());

        // 3. 填充客房统计数据
        fillRoomStatistics(report, roomData, bizDate, monthStart, yearStart,
                lastYearDate, lastYearMonthStart, lastYearYearStart);

        // 4. 填充收入统计数据
        fillIncomeStatistics(report, incomeData, bizDate, monthStart, yearStart,
                lastYearDate, lastYearMonthStart, lastYearYearStart);

        return report;
    }

    private void fillRoomStatistics(BusinessDailyReportVO report,
                                    List<ManagerDailyDO> allRoomData,
                                    LocalDate bizDate,
                                    LocalDate monthStart,
                                    LocalDate yearStart,
                                    LocalDate lastYearDate,
                                    LocalDate lastYearMonthStart,
                                    LocalDate lastYearYearStart) {
        // 当日数据
        // 从所有客房数据中查找指定日期的数据并填充至报告中
        Optional<ManagerDailyDO> todayData = findDailyData(allRoomData, bizDate);
        todayData.ifPresent(data -> {
            report.setTotalRoomNum(data.getTotalRoomNum()); // 总房间数
            report.setSoldRoomNum(data.getOvernightNum()); // 过夜房数
            report.setRepairRoomNum(data.getRepairNum()); // 维修房数
            report.setDailyRoomChangeNum(data.getOpenRoomNum()); // 开房数(自然日)
            report.setRentedRoomNum(data.getOvernightNum());
            report.setAvgRoomRate(data.getAvgRoomFee());
            report.setOccupancyRate(data.getOcc());
            report.setRevPar(data.getRevPar());
            report.setGuestCount(data.getOvernightNum());
            report.setNetRoomNum(data.getOvernightNum());
        });

        // 本月累计
        // 过滤出当月的数据并计算统计值，然后填充至报告中
        List<ManagerDailyDO> monthData = filterByDateRange(allRoomData, monthStart, bizDate);
        RoomStatistics monthStats = calculateRoomStatistics(monthData);
        report.setRentedRoomNumMonth(monthStats.rentedRoomNum);
        report.setAvgRoomRateMonth(monthStats.avgRoomRate);
        report.setOccupancyRateMonth(monthStats.occupancyRate);
        report.setRevParMonth(monthStats.revPar);

        // 本年累计
        // 过滤出当年的数据并计算统计值，然后填充至报告中
        List<ManagerDailyDO> yearData = filterByDateRange(allRoomData, yearStart, bizDate);
        RoomStatistics yearStats = calculateRoomStatistics(yearData);
        report.setRentedRoomNumYear(yearStats.rentedRoomNum);
        report.setAvgRoomRateYear(monthStats.avgRoomRate);
        report.setOccupancyRateYear(yearStats.occupancyRate);
        report.setRevParYear(yearStats.revPar);

        // 去年同期数据
        // 从所有客房数据中查找上年同期的数据并填充至报告中，同时计算同比
        Optional<ManagerDailyDO> lastYearTodayData = findDailyData(allRoomData, lastYearDate);
        lastYearTodayData.ifPresent(data -> {
            report.setRentedRoomNumLastYear(data.getOvernightNum());
            report.setAvgRoomRateLastYear(data.getAvgRoomFee());
            report.setOccupancyRateLastYear(data.getOcc());
            report.setRevParLastYear(data.getRevPar());

            // 计算同比
            report.setRentedRoomNumYoY(calculateGrowthRate(
                    report.getRentedRoomNum(), report.getRentedRoomNumLastYear()));
            report.setAvgRoomRateYoY(calculateGrowthRate(
                    report.getAvgRoomRate(), report.getAvgRoomRateLastYear()));
            report.setOccupancyRateYoY(calculateGrowthRate(
                    report.getOccupancyRate(), report.getOccupancyRateLastYear()));
            report.setRevParYoY(calculateGrowthRate(
                    report.getRevPar(), report.getRevParLastYear()));
        });

        // 去年本年累计
        // 过滤出上年同期至年底的数据并计算统计值，然后填充至报告中
        List<ManagerDailyDO> lastYearYearData = filterByDateRange(allRoomData, lastYearYearStart, lastYearDate);
        RoomStatistics lastYearYearStats = calculateRoomStatistics(lastYearYearData);
        report.setRentedRoomNumYearLastYear(lastYearYearStats.rentedRoomNum);
        report.setAvgRoomRateYearLastYear(lastYearYearStats.avgRoomRate);
        report.setOccupancyRateYearLastYear(lastYearYearStats.occupancyRate);
        report.setRevParYearLastYear(lastYearYearStats.revPar);

        // 计算年同比
        report.setRentedRoomNumYearYoY(calculateGrowthRate(
                report.getRentedRoomNumYear(), report.getRentedRoomNumYearLastYear()));
        report.setAvgRoomRateYearYoY(calculateGrowthRate(
                report.getAvgRoomRateYear(), report.getAvgRoomRateYearLastYear()));
        report.setOccupancyRateYearYoY(calculateGrowthRate(
                report.getOccupancyRateYear(), report.getOccupancyRateYearLastYear()));
        report.setRevParYearYoY(calculateGrowthRate(
                report.getRevParYear(), report.getRevParYearLastYear()));
    }

    private void fillIncomeStatistics(BusinessDailyReportVO report,
                                      List<IncomeStatDailyDO> allIncomeData,
                                      LocalDate bizDate,
                                      LocalDate monthStart,
                                      LocalDate yearStart,
                                      LocalDate lastYearDate,
                                      LocalDate lastYearMonthStart,
                                      LocalDate lastYearYearStart) {
        // 当日收入
        List<IncomeStatDailyDO> dailyItems = filterByDate(allIncomeData, bizDate);
        Map<String, Long> dailyIncome = sumIncomeByType(dailyItems);
        setIncomeFields(report, dailyIncome, "");

        // 本月收入
        List<IncomeStatDailyDO> monthItems = filterByDateRange2(allIncomeData, monthStart, bizDate);
        Map<String, Long> monthIncome = sumIncomeByType(monthItems);
        setIncomeFields(report, monthIncome, "Month");

        // 本年收入
        List<IncomeStatDailyDO> yearItems = filterByDateRange2(allIncomeData, yearStart, bizDate);
        Map<String, Long> yearIncome = sumIncomeByType(yearItems);
        setIncomeFields(report, yearIncome, "Year");

        // 去年同期收入
        List<IncomeStatDailyDO> lastYearDailyItems = filterByDate(allIncomeData, lastYearDate);
        Map<String, Long> lastYearIncome = sumIncomeByType(lastYearDailyItems);
        setIncomeFields(report, lastYearIncome, "LastYear");

        // 去年本年收入
        List<IncomeStatDailyDO> lastYearYearItems = filterByDateRange2(allIncomeData, lastYearYearStart, lastYearDate);
        Map<String, Long> lastYearYearIncome = sumIncomeByType(lastYearYearItems);
        setIncomeFields(report, lastYearYearIncome, "YearLastYear");

        // 计算同比
        calculateIncomeYoY(report);
    }

    // 辅助方法：按日期过滤数据
    private List<IncomeStatDailyDO> filterByDate(List<IncomeStatDailyDO> data, LocalDate date) {
        return data.stream()
                .filter(item -> date.equals(item.getBizDate()))
                .collect(Collectors.toList());
    }

    private List<IncomeStatDailyDO> filterByDateRange2(List<IncomeStatDailyDO> data,
                                                      LocalDate start,
                                                      LocalDate end) {
        return data.stream()
                .filter(item -> !item.getBizDate().isBefore(start)
                        && !item.getBizDate().isAfter(end))
                .collect(Collectors.toList());
    }

    private Optional<ManagerDailyDO> findDailyData(List<ManagerDailyDO> data, LocalDate date) {
        return data.stream()
                .filter(item -> date.equals(item.getBizDate()))
                .findFirst();
    }

    private List<ManagerDailyDO> filterByDateRange(List<ManagerDailyDO> data,
                                                   LocalDate start,
                                                   LocalDate end) {
        return data.stream()
                .filter(item -> !item.getBizDate().isBefore(start)
                        && !item.getBizDate().isAfter(end))
                .collect(Collectors.toList());
    }

    // 计算客房统计数据
    private RoomStatistics calculateRoomStatistics(List<ManagerDailyDO> dataList) {
        RoomStatistics stats = new RoomStatistics();

        if (dataList.isEmpty()) {
            return stats;
        }

        // 计算出租房总数
        stats.rentedRoomNum = dataList.stream()
                .mapToInt(ManagerDailyDO::getOvernightNum)
                .sum();

        // 计算平均房价
        double avgRoomRate = dataList.stream()
                .mapToLong(ManagerDailyDO::getAvgRoomFee)
                .average()
                .orElse(0);
        stats.avgRoomRate = (long) avgRoomRate;

        // 计算平均出租率
        double avgOccupancyRate = dataList.stream()
                .mapToDouble(d -> d.getOcc().doubleValue())
                .average()
                .orElse(0);
        stats.occupancyRate = BigDecimal.valueOf(avgOccupancyRate);

        // 计算平均RevPAR
        double avgRevPar = dataList.stream()
                .mapToLong(ManagerDailyDO::getRevPar)
                .average()
                .orElse(0);
        stats.revPar = (long) avgRevPar;

        return stats;
    }

    // 按收入类型汇总
    private Map<String, Long> sumIncomeByType(List<IncomeStatDailyDO> items) {
        return items.stream()
                .collect(Collectors.groupingBy(
                        IncomeStatDailyDO::getSubType,
                        Collectors.summingLong(IncomeStatDailyDO::getFee)
                ));
    }

    // 设置收入字段
    private void setIncomeFields(BusinessDailyReportVO report,
                                 Map<String, Long> incomeMap,
                                 String suffix) {
        try {
            // 总营业收入
            setField(report, "totalIncome" + suffix,
                    incomeMap.values().stream().mapToLong(Long::longValue).sum());

            // 商场收入
            setField(report, "mallIncome" + suffix, incomeMap.getOrDefault("MALL", 0L));

            // 会籍服务
            setField(report, "membershipServiceIncome" + suffix, incomeMap.getOrDefault("MEMBERSHIP", 0L));

            // 会员充值
            setField(report, "memberRecharge" + suffix, incomeMap.getOrDefault("RECHARGE", 0L));

            // 其他收入
            setField(report, "otherIncome" + suffix, incomeMap.getOrDefault("OTHER", 0L));

            // 物品赔偿
            setField(report, "itemCompensation" + suffix, incomeMap.getOrDefault("COMPENSATION", 0L));

            // 会议
            setField(report, "meetingIncome" + suffix, incomeMap.getOrDefault("MEETING", 0L));

            // 其他
            setField(report, "otherIncomeDetail" + suffix, incomeMap.getOrDefault("OTHER_DETAIL", 0L));

            // 餐饮收入
            setField(report, "foodIncome" + suffix, incomeMap.getOrDefault("FOOD", 0L));

            // 早餐
            setField(report, "breakfastIncome" + suffix, incomeMap.getOrDefault("BREAKFAST", 0L));

            // 正餐
            setField(report, "dinnerIncome" + suffix, incomeMap.getOrDefault("DINNER", 0L));

        } catch (Exception e) {
            throw new RuntimeException("设置收入字段失败", e);
        }
    }

    // 反射设置字段值
    private void setField(BusinessDailyReportVO report, String fieldName, Object value)
            throws NoSuchFieldException, IllegalAccessException {
        java.lang.reflect.Field field = report.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(report, value);
    }

    // 计算同比
    private void calculateIncomeYoY(BusinessDailyReportVO report) {
        // 总营业收入同比
        report.setTotalIncomeYoY(calculateGrowthRate(
                report.getTotalIncome(), report.getTotalIncomeLastYear()));
        report.setTotalIncomeYearYoY(calculateGrowthRate(
                report.getTotalIncomeYear(), report.getTotalIncomeYearLastYear()));

        // 商场收入同比
        report.setMallIncomeYoY(calculateGrowthRate(
                report.getMallIncome(), report.getMallIncomeLastYear()));
        report.setMallIncomeYearYoY(calculateGrowthRate(
                report.getMallIncomeYear(), report.getMallIncomeYearLastYear()));

        // 其他收入项同比...
    }

    // 计算增长率
    private BigDecimal calculateGrowthRate(Number current, Number lastYear) {
        if (lastYear == null || lastYear.longValue() == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(
                        (current.doubleValue() - lastYear.doubleValue()) * 100 / lastYear.doubleValue())
                .setScale(2, RoundingMode.HALF_UP);
    }

    // 客房统计中间结果
    private static class RoomStatistics {
        int rentedRoomNum;
        long avgRoomRate;
        BigDecimal occupancyRate;
        long revPar;
    }
}