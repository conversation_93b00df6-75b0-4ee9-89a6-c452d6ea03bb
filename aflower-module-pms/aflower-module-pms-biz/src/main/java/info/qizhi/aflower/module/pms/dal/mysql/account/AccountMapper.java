package info.qizhi.aflower.module.pms.dal.mysql.account;

import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.mybatis.core.mapper.BaseMapperX;
import info.qizhi.aflower.framework.mybatis.core.query.LambdaQueryWrapperX;
import info.qizhi.aflower.module.pms.controller.admin.account.vo.*;
import info.qizhi.aflower.module.pms.controller.admin.accset.vo.AccRecordPageReqVO;
import info.qizhi.aflower.module.pms.controller.admin.accset.vo.AccRecordStatByBizDateRespVO;
import info.qizhi.aflower.module.pms.controller.admin.arset.vo.AccountChooseReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.PayOrConsumeDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.TransactionDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.TransferAccountReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.account.AccountDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static info.qizhi.aflower.framework.common.enums.PayAccountEnum.*;

/**
 * 账务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountMapper extends BaseMapperX<AccountDO> {

    /**
     * 统计单号下宾客代码的消费、付款合计
     *
     * @param reqVO
     * @return
     */
    List<AccountStatRespVO> statTogetherAccountByNoList(AccountStatReqVO reqVO);

    /**
     * 统计多个宾客代码的消费、付款合计
     *
     * @param reqVO
     * @return
     */
    AccountStatByTogetherCodesRespVO statTogetherAccountByTogetherCodes(AccountStatByTogetherCodesReqVO reqVO);

    /**
     * 按照账号、账务类型进行分类统计消费、付款合计
     *
     * @param gcode   集团代码
     * @param hcode   门店代码
     * @param bizDate 营业日期
     * @return List<AccountStatByBizDateRespVO>
     */
    List<AccountStatByBizDateRespVO> statAccountByBizDate(@Param("gcode") String gcode,
                                                          @Param("hcode") String hcode,
                                                          @Param("bizDate") LocalDate bizDate,
                                                          @Param("accType") String accType
    );

    /**
     * 统计当前班次下的结账业务，按照结账号批次统计
     *
     * @param gcode        集团代码
     * @param hcode        门店代码
     * @param togetherCode 客单代码
     * @param payShiftNo   付款班次
     * @param bizDate      付款营业日
     * @return
     */
    List<CloseAccountRespVO> getCloseAccountSumByTogetherCode(@Param("gcode") String gcode,
                                                              @Param("hcode") String hcode,
                                                              @Param("togetherCode") String togetherCode,
                                                              @Param("payShiftNo") String payShiftNo,
                                                              @Param("bizDate") LocalDate bizDate);


    void updateBatchById(@Param("list") List<AccountDO> accountDOList);

    /**
     * 应收账获取账务分页
     *
     * @param reqVO
     * @return
     */
    default PageResult<AccountDO> selectPageByArSet(AccountPageReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> queryWrapperX = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .eq(AccountDO::getPayCode, reqVO.getArSetCode())
                .eqIfPresent(AccountDO::getIsVerify, reqVO.getIsVerify())
                .eqIfPresent(AccountDO::getState, reqVO.getState());
        // 关键字过滤
        if (StrUtil.isNotBlank(reqVO.getKeyWords())) {
            queryWrapperX.and(q -> {
                q.eq(AccountDO::getPayer, reqVO.getKeyWords())
                        .or().eq(AccountDO::getRNo, reqVO.getKeyWords())
                        .or().eq(AccountDO::getNo, reqVO.getKeyWords())
                        .or().likeRight(AccountDO::getRemark, reqVO.getKeyWords())
                        .or().eq(AccountDO::getOutOrderNo, reqVO.getKeyWords());
            });
        }
        // 营业日 挂账日期
        if (StrUtil.isNotBlank(reqVO.getDateRange())) {
            if (NumberEnum.ZERO.getNumber().equals(reqVO.getDateRange())) {
                queryWrapperX.betweenIfPresent(AccountDO::getCreateTime, reqVO.getStartDate(), reqVO.getEndDate());
            } else if (NumberEnum.ONE.getNumber().equals(reqVO.getDateRange())) {
                queryWrapperX.betweenIfPresent(AccountDO::getBizDate, reqVO.getStartDate(), reqVO.getEndDate());
            }
        }
        // 根据创建时间降序排序
        queryWrapperX.orderByDesc(AccountDO::getCreateTime);

        return selectPage(reqVO, queryWrapperX);
    }

    /**
     * 获取现付账分页
     *
     * @param reqVO
     * @return
     */
    default PageResult<AccountDO> selectCashBillPage(AccRecordPageReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> queryWrapperX = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .eqIfPresent(AccountDO::getPayCode, reqVO.getArSetCode())
                .eqIfPresent(AccountDO::getTogetherCode, reqVO.getAccCode())
                .eqIfPresent(AccountDO::getIsVerify, reqVO.getIsVerify())
                .eq(AccountDO::getAccType, AccountTypeEnum.CASH.getCode())
                .orderByDesc(AccountDO::getId);
        if (Objects.equals(reqVO.getOffset(), BooleanEnum.TRUE.getValue())) {
            queryWrapperX.eq(AccountDO::getState, AccountStatusEnum.REDEEMED.getCode());
        }
        if (Objects.equals(reqVO.getOffset(), BooleanEnum.FALSE.getValue())) {
            queryWrapperX.ne(AccountDO::getState, AccountStatusEnum.REDEEMED.getCode());
        }
        // 营业日 挂账日期
        if (StrUtil.isNotBlank(reqVO.getDateRange())) {
            if (NumberEnum.ZERO.getNumber().equals(reqVO.getDateRange())) {
                queryWrapperX.betweenIfPresent(AccountDO::getCreateTime, reqVO.getStartDate(), reqVO.getEndDate());
            } else if (NumberEnum.ONE.getNumber().equals(reqVO.getDateRange())) {
                queryWrapperX.betweenIfPresent(AccountDO::getBizDate, reqVO.getStartDate(), reqVO.getEndDate());
            }
        }
        return selectPage(reqVO, queryWrapperX);
    }

    /**
     * 账务分页，不包括预授权的账务
     *
     * @param reqVO
     * @return
     */
    default PageResult<AccountDO> selectPage(AccountPageReq2VO reqVO) {
        LambdaQueryWrapperX<AccountDO> queryWrapperX = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .eqIfPresent(AccountDO::getState, reqVO.getState())
                .inIfPresent(AccountDO::getTogetherCode, reqVO.getTogetherCodes())
                .inIfPresent(AccountDO::getNo, reqVO.getNoList())
                .inIfPresent(AccountDO::getSubCode, reqVO.getSubCodes())
                .betweenIfPresent(AccountDO::getBizDate, reqVO.getStartDate(), reqVO.getEndDate());
        queryWrapperX.notIn(AccountDO::getSubCode, PayAccountEnum.BANK_PRE_AUTH.getCode(), SCAN_GUN_PRE_AUTH.getCode())
                .orderByDesc(AccountDO::getId);
        return selectPage(reqVO, queryWrapperX);
    }


    default AccountDO selectOne(AccountReqVO reqVO) {
        return selectOne(new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .eq(AccountDO::getAccNo, reqVO.getAccNo())
        );
    }

    default List<AccountDO> selectList(AccountListReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> queryWrapperX = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eqIfPresent(AccountDO::getHcode, reqVO.getHcode())
                .eqIfPresent(AccountDO::getState, reqVO.getState())
                .eqIfPresent(AccountDO::getBizDate, reqVO.getBizDate())
                .eqIfPresent(AccountDO::getTogetherCode, reqVO.getTogetherCode())
                .eqIfPresent(AccountDO::getRNo, reqVO.getRNo())
                .eqIfPresent(AccountDO::getIsRev, reqVO.getIsRev())
                .inIfPresent(AccountDO::getTogetherCode, reqVO.getTogetherCodes())
                .eqIfPresent(AccountDO::getNo, reqVO.getNo())
                .inIfPresent(AccountDO::getNo, reqVO.getNos())
                .inIfPresent(AccountDO::getSubCode, reqVO.getSubCodes())
                .inIfPresent(AccountDO::getAccNo, reqVO.getAccNos())
                .eqIfPresent(AccountDO::getRecorder, reqVO.getRecorder())
                .eqIfPresent(AccountDO::getShiftNo, reqVO.getShiftNo())
                .eqIfPresent(AccountDO::getPayShiftNo, reqVO.getPayShiftNo())
                .eqIfPresent(AccountDO::getPayer, reqVO.getPayOperator());
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null) {
            queryWrapperX.between(AccountDO::getPayBizDate, reqVO.getStartDate(), reqVO.getEndDate());
        } else {
            queryWrapperX.eqIfPresent(AccountDO::getPayBizDate, reqVO.getPayBizDate());
        }
        return selectList(queryWrapperX);
    }

    /**
     * 查询账务列表 - 支持 shiftNo/payShiftNo 和 bizDate/payBizDate 查询
     * 用于交班报表(收付实现制)，查询入账班次或结账班次、入账营业日或结账营业日匹配的账务数据
     */
    default List<AccountDO> selectListForHandoverCashRealization(AccountListReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> queryWrapperX = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eqIfPresent(AccountDO::getHcode, reqVO.getHcode());

        // 支持 bizDate 或 payBizDate 查询
        if (reqVO.getBizDate() != null) {
            queryWrapperX.and(wrapper -> wrapper
                    .eq(AccountDO::getBizDate, reqVO.getBizDate())
                    .or()
                    .eq(AccountDO::getPayBizDate, reqVO.getBizDate())
            );
        }

        // 支持 shiftNo 或 payShiftNo 查询
        if (reqVO.getShiftNo() != null) {
            queryWrapperX.and(wrapper -> wrapper
                    .eq(AccountDO::getShiftNo, reqVO.getShiftNo())
                    .or()
                    .eq(AccountDO::getPayShiftNo, reqVO.getShiftNo())
            );
        }

        return selectList(queryWrapperX.orderByDesc(AccountDO::getId));
    }

    /**
     * 根据请求条件选择账户子列表
     * 此方法用于根据给定的请求参数，查询符合条件的账户子列表
     * 它构建了一个查询条件，包括公司代码（Gcode）、酒店代码（Hcode）、组合代码列表（TogetherCodes）和编号列表（NoList），
     * 并根据这些条件从数据库中选择出对应的子账户代码（SubCode）和子账户类型（SubType）
     *
     * @param reqVO 包含请求参数的对象，包括公司代码、酒店代码、组合代码列表和编号列表
     * @return 返回一个账户数据对象列表，包含匹配条件的子账户代码和类型
     */
    default List<AccountDO> selectAccountSubList(AccountSubReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> queryWrapperX = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eqIfPresent(AccountDO::getHcode, reqVO.getHcode())
                .inIfPresent(AccountDO::getTogetherCode, reqVO.getTogetherCodes())
                .inIfPresent(AccountDO::getNo, reqVO.getNoList());
        queryWrapperX.select(AccountDO::getSubCode, AccountDO::getSubType);
        return selectList(queryWrapperX);
    }

    default List<AccountDO> selectSettleList(SettleAccountListReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> queryWrapperX = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eqIfPresent(AccountDO::getHcode, reqVO.getHcode())
                .eqIfPresent(AccountDO::getState, reqVO.getState())
                .eqIfPresent(AccountDO::getBizDate, reqVO.getBizDate())
                .eqIfPresent(AccountDO::getTogetherCode, reqVO.getTogetherCode())
                .eqIfPresent(AccountDO::getRNo, reqVO.getRNo())
                .inIfPresent(AccountDO::getTogetherCode, reqVO.getTogetherCodes())
                .eqIfPresent(AccountDO::getNo, reqVO.getNo())
                .inIfPresent(AccountDO::getNo, reqVO.getNos())
                .inIfPresent(AccountDO::getSubCode, reqVO.getSubCodes())
                .inIfPresent(AccountDO::getAccNo, reqVO.getAccNos())
                .eqIfPresent(AccountDO::getPayer, reqVO.getPayOperator());
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null) {
            if (reqVO.getTimeType().equals(NumberEnum.ONE.getNumber())) {
                queryWrapperX.between(AccountDO::getPayBizDate, reqVO.getStartDate(), reqVO.getEndDate());
            } else {
                queryWrapperX.between(AccountDO::getPayTime, reqVO.getStartDate(), reqVO.getEndDate());
            }
        } else {
            queryWrapperX.eqIfPresent(AccountDO::getPayBizDate, reqVO.getPayBizDate());
        }
        return selectList(queryWrapperX);
    }

    default List<AccountDO> selectListForReport(AccountListReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> queryWrapperX = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .eqIfPresent(AccountDO::getSubCode, reqVO.getSubCodes())
                .eqIfPresent(AccountDO::getRecorder, reqVO.getRecorder())
                .eqIfPresent(AccountDO::getPayCode, reqVO.getArSetCode())
                .eqIfPresent(AccountDO::getSubType, reqVO.getSubType())
                .inIfPresent(AccountDO::getCreditTargetType, reqVO.getCreditTargetType());
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null) {
            queryWrapperX.between(AccountDO::getBizDate, reqVO.getStartDate(), reqVO.getEndDate());
        } else {
            queryWrapperX.eqIfPresent(AccountDO::getBizDate, reqVO.getBizDate());
        }
        if (StrUtil.isNotBlank(reqVO.getNo())) {
            queryWrapperX.and(q -> {
                q.eq(AccountDO::getNo, reqVO.getNo())
                        .or().eq(AccountDO::getOutOrderNo, reqVO.getNo());
            });
        }
        return selectList(queryWrapperX);
    }

    default List<AccountDO> selectPayOrConsumeList(PayOrConsumeDetailReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> queryWrapperX = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .eqIfPresent(AccountDO::getRecorder, reqVO.getOperator())
                .eqIfPresent(AccountDO::getSubType, reqVO.getSubType())
                .inIfPresent(AccountDO::getShiftNo, reqVO.getShiftNos())
                .inIfPresent(AccountDO::getSubCode, reqVO.getSubCodes());
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null) {
            if (NumberEnum.ONE.getNumber().equals(reqVO.getTimeType())) {
                queryWrapperX.between(AccountDO::getBizDate, reqVO.getStartDate(), reqVO.getEndDate());
            } else {
                queryWrapperX.between(AccountDO::getCreateTime, reqVO.getStartDate(), reqVO.getEndDate());
            }
        } /*else {
            queryWrapperX.eqIfPresent(AccountDO::getBizDate, reqVO.getBizDate());
        }*/
      /*  if (StrUtil.isNotBlank(reqVO.getNo())) {
            queryWrapperX.and(q -> {
                q.eq(AccountDO::getNo, reqVO.getNo())
                        .or().eq(AccountDO::getOutOrderNo, reqVO.getNo());
            });
        }*/
        return selectList(queryWrapperX);
    }

    default List<AccountDO> selectList2(AccountReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .inIfPresent(AccountDO::getAccNo, reqVO.getAccNoList())
                .eqIfPresent(AccountDO::getState, reqVO.getState())
        );
    }

    /**
     * 获取某结账批次的账务
     *
     * @param gcode 集团代码
     * @param hcode 门店代码
     * @param payNo 结账号
     * @return
     */
    default List<AccountDO> selectListByPayNo(String gcode, String hcode, String payNo) {
        return selectList(new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, gcode)
                .eq(AccountDO::getHcode, hcode)
                .eq(AccountDO::getState, AccountStatusEnum.CLOSED.getCode())
                .eq(AccountDO::getPayNo, payNo)
        );
    }

    /**
     * 查询客人中未结算的账务
     *
     * @param reqVO
     * @return
     */
    default List<AccountDO> selectOpenAccountByTogetherCodes(AccountReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .eq(AccountDO::getState, AccountStatusEnum.UNCLOSED.getCode())
                .in(AccountDO::getTogetherCode, reqVO.getTogetherCodeList()));
    }

    /**
     * 查询宾客名下的预授权账务列表
     *
     * @param reqVO 入参
     * @return 返回预授权列表
     */
    default List<AccountDO> selectPreAuthAccountList(ConfirmPreAuthListReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> query = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .in(AccountDO::getTogetherCode, reqVO.getTogetherCodeList());
        query.and(q -> {
            q.in(AccountDO::getSubCode, List.of(PayAccountEnum.BANK_PRE_AUTH.getCode(), SCAN_GUN_PRE_AUTH.getCode()))
                    .or()
                    .eq(AccountDO::getIsPreAuthAffirm, NumberEnum.ONE.getNumber());
        });
        return selectList(query);
    }

    default List<AccountDO> selectListForHandoverReport(HandoverReportReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> query = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .eqIfPresent(AccountDO::getSubCode, reqVO.getSubCode())
                .inIfPresent(AccountDO::getShiftNo, reqVO.getShiftNo())
                .eqIfPresent(AccountDO::getRecorder, reqVO.getOperator());
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null) {
            query.between(AccountDO::getBizDate, reqVO.getStartDate(), reqVO.getEndDate());
        } else {
            query.eq(AccountDO::getBizDate, reqVO.getBizDate());
        }
        if (StrUtil.isNotBlank(reqVO.getKeyWords())) {
            query.and(q -> {
                q.eq(AccountDO::getGuestName, reqVO.getKeyWords())
                        .or().eq(AccountDO::getRNo, reqVO.getKeyWords())
                        .or().eq(AccountDO::getFee, reqVO.getKeyWords());
            });
        }
        return selectList(query);
    }

    default List<AccountDO> selectList(AccountChooseReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .in(AccountDO::getAccNo, reqVO.getAccNoList()));
    }

    default PageResult<AccountDO> selectTransactionPage(TransactionPageReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> query = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eqIfPresent(AccountDO::getNo, reqVO.getNo())
                .eqIfPresent(AccountDO::getGuestName, reqVO.getGuestName())
                .eqIfPresent(AccountDO::getRNo, reqVO.getRNo())
                .eqIfPresent(AccountDO::getOutOrderNo, reqVO.getOutOrderNo())
                .eqIfPresent(AccountDO::getFee, reqVO.getFee())
                .in(AccountDO::getSubCode, Arrays.asList(
                        SCAN_GUN_PRE_AUTH,
                        SCAN_GUN_WX,
                        SCAN_GUN_WX_REFUND,
                        SCAN_GUN_ALIPAY,
                        SCAN_GUN_ALIPAY_REFUND,
                        SCAN_GUN))
                .betweenIfPresent(AccountDO::getCreateTime, reqVO.getStartTime(), reqVO.getEndTime());

        // 排序，默认按时间倒序
        query.orderByDesc(AccountDO::getCreateTime);

        // 执行分页查询
        return selectPage(reqVO, query);
    }

    /**
     * 根据营业日统计现付账消费、付款合计
     *
     * @param gcode   集团代码
     * @param hcode   门店代码
     * @param bizDate 营业日期
     * @return 统计结果
     */
    List<AccRecordStatByBizDateRespVO> statAccRecordByBizDate(@Param("gcode") String gcode,
                                                              @Param("hcode") String hcode,
                                                              @Param("bizDate") LocalDate bizDate,
                                                              @Param("accType") String accType);

    default List<AccountDO> selectRefundAccountList(AccountChooseReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .in(AccountDO::getRefundAccNo, reqVO.getAccNoList()));
    }

    default List<AccountDO> selectTransactionReport(TransactionDetailReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> queryWrapperX = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eqIfPresent(AccountDO::getHcode, reqVO.getHcode())
                .eqIfPresent(AccountDO::getFee, reqVO.getFee())
                .in(AccountDO::getSubCode, Arrays.asList(
                        SCAN_GUN_PRE_AUTH,
                        SCAN_GUN_WX,
                        SCAN_GUN_WX_REFUND,
                        SCAN_GUN_ALIPAY,
                        SCAN_GUN_ALIPAY_REFUND,
                        SCAN_GUN));
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null) {
            if (NumberEnum.ONE.getNumber().equals(reqVO.getTimeType())) {
                queryWrapperX.between(AccountDO::getBizDate, reqVO.getStartDate(), reqVO.getEndDate());
            } else {
                queryWrapperX.between(AccountDO::getCreateTime, reqVO.getStartDate(), reqVO.getEndDate());
            }
        }
        if (StrUtil.isNotBlank(reqVO.getKeyWords())) {
            queryWrapperX.and(q -> {
                q.eq(AccountDO::getNo, reqVO.getKeyWords())
                        .or().eq(AccountDO::getGuestName, reqVO.getKeyWords())
                        .or().eq(AccountDO::getRNo, reqVO.getKeyWords())
                        .or().eq(AccountDO::getOutOrderNo, reqVO.getKeyWords());
            });
        }
        return selectList(queryWrapperX);
    }

    default List<AccountDO> selectChangeList(TransferAccountReqVO reqVO) {
        LambdaQueryWrapperX<AccountDO> queryWrapperX = new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getGcode, reqVO.getGcode())
                .eq(AccountDO::getHcode, reqVO.getHcode())
                .eqIfPresent(AccountDO::getRecorder, reqVO.getOperator());

        if(NumberEnum.ONE.getNumber().equals(reqVO.getState())){
            queryWrapperX.in(AccountDO::getIsTurnOutIn, List.of(NumberEnum.ONE.getNumber(), NumberEnum.MINUS.getNumber()));
        }
        if(NumberEnum.TWO.getNumber().equals(reqVO.getState())){
            queryWrapperX.eq(AccountDO::getIsSplit, NumberEnum.ONE.getNumber());
        }
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null) {
            queryWrapperX.between(AccountDO::getBizDate, reqVO.getStartDate(), reqVO.getEndDate());
        }
        return selectList(queryWrapperX);
    }

}